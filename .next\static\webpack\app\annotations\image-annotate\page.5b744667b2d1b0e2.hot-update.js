"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./app/annotations/image-annotate/page.tsx":
/*!*************************************************!*\
  !*** ./app/annotations/image-annotate/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageAnnotatePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/routes */ \"(app-pages-browser)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/annotation/image-annotation-canvas */ \"(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\");\n/* harmony import */ var _components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/annotation/annotation-toolbar */ \"(app-pages-browser)/./components/annotation/annotation-toolbar.tsx\");\n/* harmony import */ var _components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/annotation/annotation-list */ \"(app-pages-browser)/./components/annotation/annotation-list.tsx\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ImageAnnotatePage() {\n    _s();\n    const [currentTool, setCurrentTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [annotations, setAnnotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnnotation, setSelectedAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentLabel, setCurrentLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showLabels, setShowLabels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Load image from URL params or show selector\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotatePage.useEffect\": ()=>{\n            const imageId = searchParams.get('imageId');\n            const datasetId = searchParams.get('datasetId');\n            if (imageId) {\n                loadImageById(imageId);\n            } else if (datasetId) {\n                loadRandomImageFromDataset(datasetId);\n            } else {\n                // Load a default image for immediate use\n                loadDefaultImage();\n            }\n        }\n    }[\"ImageAnnotatePage.useEffect\"], [\n        searchParams\n    ]);\n    const loadDefaultImage = async ()=>{\n        setLoading(true);\n        try {\n            // Load the first available image as default\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage();\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            } else {\n                // If no images available, show selector\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load default image:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadImageById = async (imageId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getImage(imageId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRandomImageFromDataset = async (datasetId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage(datasetId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load random image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Predefined labels with colors\n    const predefinedLabels = [\n        {\n            name: '人物',\n            color: '#ef4444'\n        },\n        {\n            name: '车辆',\n            color: '#3b82f6'\n        },\n        {\n            name: '建筑',\n            color: '#10b981'\n        },\n        {\n            name: '动物',\n            color: '#f59e0b'\n        },\n        {\n            name: '植物',\n            color: '#8b5cf6'\n        },\n        {\n            name: '物体',\n            color: '#ec4899'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    // Event handlers\n    const handleAnnotationCreate = (annotation)=>{\n        setAnnotations((prev)=>[\n                ...prev,\n                annotation\n            ]);\n        saveToHistory([\n            ...annotations,\n            annotation\n        ]);\n    };\n    const handleAnnotationUpdate = (id, updates)=>{\n        setAnnotations((prev)=>prev.map((ann)=>ann.id === id ? {\n                    ...ann,\n                    ...updates\n                } : ann));\n    };\n    const handleAnnotationDelete = (id)=>{\n        setAnnotations((prev)=>prev.filter((ann)=>ann.id !== id));\n        setSelectedAnnotation(null);\n        saveToHistory(annotations.filter((ann)=>ann.id !== id));\n    };\n    const handleAnnotationVisibilityToggle = (id)=>{\n        // For now, just select/deselect the annotation\n        setSelectedAnnotation(selectedAnnotation === id ? null : id);\n    };\n    const saveToHistory = (newAnnotations)=>{\n        const annotationsToSave = newAnnotations || annotations;\n        const newHistory = history.slice(0, historyIndex + 1);\n        newHistory.push([\n            ...annotationsToSave\n        ]);\n        setHistory(newHistory);\n        setHistoryIndex(newHistory.length - 1);\n    };\n    const handleUndo = ()=>{\n        if (historyIndex > 0) {\n            setHistoryIndex(historyIndex - 1);\n            setAnnotations([\n                ...history[historyIndex - 1]\n            ]);\n        }\n    };\n    const handleRedo = ()=>{\n        if (historyIndex < history.length - 1) {\n            setHistoryIndex(historyIndex + 1);\n            setAnnotations([\n                ...history[historyIndex + 1]\n            ]);\n        }\n    };\n    const handleClear = ()=>{\n        if (window.confirm('确定要清空所有标注吗？')) {\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n            saveToHistory([]);\n        }\n    };\n    const handleSave = ()=>{\n        // Save annotations logic here\n        console.log('Saving annotations:', annotations);\n        alert(\"已保存 \".concat(annotations.length, \" 个标注\"));\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.ANNOTATIONS);\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载图像...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    }\n    // Show image selector if no image is selected\n    if (showImageSelector || !selectedImage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"选择要标注的图像\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"选择图像进行标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"点击下方图像开始标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        {\n                                            id: 'img_001',\n                                            name: '猫咪图片',\n                                            url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_002',\n                                            name: '街景图片',\n                                            url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_003',\n                                            name: '狗狗图片',\n                                            url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_004',\n                                            name: '建筑图片',\n                                            url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_005',\n                                            name: '风景图片',\n                                            url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'\n                                        }\n                                    ].map((img)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"cursor-pointer rounded-lg border-2 border-gray-200 hover:border-blue-500 transition-colors\",\n                                            onClick: ()=>{\n                                                setSelectedImage({\n                                                    id: img.id,\n                                                    name: img.name,\n                                                    url: img.url,\n                                                    width: 800,\n                                                    height: 600,\n                                                    size: 200000,\n                                                    format: 'jpg',\n                                                    datasetId: '1',\n                                                    uploadedAt: new Date(),\n                                                    tags: []\n                                                });\n                                                setShowImageSelector(false);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: img.url,\n                                                        alt: img.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: img.name\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, img.id, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: selectedImage.name\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"inline-flex items-center justify-center h-9 rounded-md px-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground text-sm\",\n                            onClick: ()=>setShowImageSelector(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                \"切换图像\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__.AnnotationToolbar, {\n                                currentTool: currentTool,\n                                onToolChange: setCurrentTool,\n                                currentLabel: currentLabel,\n                                onLabelChange: setCurrentLabel,\n                                predefinedLabels: predefinedLabels,\n                                zoom: zoom,\n                                onZoomChange: setZoom,\n                                showLabels: showLabels,\n                                onShowLabelsToggle: ()=>setShowLabels(!showLabels),\n                                canUndo: historyIndex > 0,\n                                canRedo: historyIndex < history.length - 1,\n                                onUndo: handleUndo,\n                                onRedo: handleRedo,\n                                onSave: handleSave,\n                                onClear: handleClear\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__.AnnotationList, {\n                                annotations: annotations,\n                                selectedAnnotation: selectedAnnotation,\n                                onAnnotationSelect: setSelectedAnnotation,\n                                onAnnotationDelete: handleAnnotationDelete,\n                                onAnnotationUpdate: handleAnnotationUpdate,\n                                onAnnotationVisibilityToggle: handleAnnotationVisibilityToggle\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"图像标注区域\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                \"使用左侧工具在图像上创建标注 - 当前工具: \",\n                                                currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-auto border rounded-lg bg-gray-50\",\n                                            style: {\n                                                maxHeight: '600px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"scale(\".concat(zoom, \")\"),\n                                                    transformOrigin: 'top left'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__.ImageAnnotationCanvas, {\n                                                    imageUrl: selectedImage.url,\n                                                    imageWidth: selectedImage.width,\n                                                    imageHeight: selectedImage.height,\n                                                    annotations: annotations,\n                                                    selectedAnnotation: selectedAnnotation,\n                                                    currentTool: currentTool,\n                                                    currentLabel: currentLabel,\n                                                    currentColor: getCurrentColor(),\n                                                    zoom: zoom,\n                                                    showLabels: showLabels,\n                                                    onAnnotationCreate: handleAnnotationCreate,\n                                                    onAnnotationUpdate: handleAnnotationUpdate,\n                                                    onAnnotationSelect: setSelectedAnnotation\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"使用说明:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 选择工具和标签后，在图像上拖拽创建标注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 点击标注可以选中，在右侧列表中编辑标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 使用缩放工具可以放大查看细节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 支持撤销/重做操作，可以随时恢复之前的状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"总标注数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.filter((a)=>a.label).length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"已标记\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                Math.round(zoom * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"缩放比例\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: history.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"历史记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotatePage, \"9pHOkhHZxupwN/FxwGc1+4XtiAM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ImageAnnotatePage;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hbm5vdGF0aW9ucy9pbWFnZS1hbm5vdGF0ZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDaUI7QUFDb0M7QUFDckQ7QUFDaUI7QUFDbUQ7QUFDVjtBQUN0QjtBQUV6QjtBQUV2QyxTQUFTaUI7O0lBQ3RCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHbkIsK0NBQVFBLENBQWlCO0lBQy9ELE1BQU0sQ0FBQ29CLGFBQWFDLGVBQWUsR0FBR3JCLCtDQUFRQSxDQUFvQixFQUFFO0lBQ3BFLE1BQU0sQ0FBQ3NCLG9CQUFvQkMsc0JBQXNCLEdBQUd2QiwrQ0FBUUEsQ0FBZ0I7SUFDNUUsTUFBTSxDQUFDd0IsY0FBY0MsZ0JBQWdCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMwQixNQUFNQyxRQUFRLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNLENBQUM0QixZQUFZQyxjQUFjLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM4QixTQUFTQyxXQUFXLEdBQUcvQiwrQ0FBUUEsQ0FBc0IsRUFBRTtJQUM5RCxNQUFNLENBQUNnQyxjQUFjQyxnQkFBZ0IsR0FBR2pDLCtDQUFRQSxDQUFDLENBQUM7SUFDbEQsTUFBTSxDQUFDa0MsZUFBZUMsaUJBQWlCLEdBQUduQywrQ0FBUUEsQ0FBbUI7SUFDckUsTUFBTSxDQUFDb0MsbUJBQW1CQyxxQkFBcUIsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ3NDLFNBQVNDLFdBQVcsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBRXZDLE1BQU13QyxTQUFTdEMsMERBQVNBO0lBQ3hCLE1BQU11QyxlQUFldEMsZ0VBQWVBO0lBRXBDLDhDQUE4QztJQUM5Q0YsZ0RBQVNBO3VDQUFDO1lBQ1IsTUFBTXlDLFVBQVVELGFBQWFFLEdBQUcsQ0FBQztZQUNqQyxNQUFNQyxZQUFZSCxhQUFhRSxHQUFHLENBQUM7WUFFbkMsSUFBSUQsU0FBUztnQkFDWEcsY0FBY0g7WUFDaEIsT0FBTyxJQUFJRSxXQUFXO2dCQUNwQkUsMkJBQTJCRjtZQUM3QixPQUFPO2dCQUNMLHlDQUF5QztnQkFDekNHO1lBQ0Y7UUFDRjtzQ0FBRztRQUFDTjtLQUFhO0lBRWpCLE1BQU1NLG1CQUFtQjtRQUN2QlIsV0FBVztRQUNYLElBQUk7WUFDRiw0Q0FBNEM7WUFDNUMsTUFBTVMsUUFBUSxNQUFNaEMsZ0VBQVlBLENBQUNpQyxjQUFjO1lBQy9DLElBQUlELE9BQU87Z0JBQ1RiLGlCQUFpQmE7Z0JBQ2pCWCxxQkFBcUI7WUFDdkIsT0FBTztnQkFDTCx3Q0FBd0M7Z0JBQ3hDQSxxQkFBcUI7WUFDdkI7UUFDRixFQUFFLE9BQU9hLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0NiLHFCQUFxQjtRQUN2QixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTU0sZ0JBQWdCLE9BQU9IO1FBQzNCSCxXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU1TLFFBQVEsTUFBTWhDLGdFQUFZQSxDQUFDb0MsUUFBUSxDQUFDVjtZQUMxQyxJQUFJTSxPQUFPO2dCQUNUYixpQkFBaUJhO2dCQUNqQlgscUJBQXFCO1lBQ3ZCO1FBQ0YsRUFBRSxPQUFPYSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3pDLFNBQVU7WUFDUlgsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNTyw2QkFBNkIsT0FBT0Y7UUFDeENMLFdBQVc7UUFDWCxJQUFJO1lBQ0YsTUFBTVMsUUFBUSxNQUFNaEMsZ0VBQVlBLENBQUNpQyxjQUFjLENBQUNMO1lBQ2hELElBQUlJLE9BQU87Z0JBQ1RiLGlCQUFpQmE7Z0JBQ2pCWCxxQkFBcUI7WUFDdkI7UUFDRixFQUFFLE9BQU9hLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDaEQsU0FBVTtZQUNSWCxXQUFXO1FBQ2I7SUFDRjtJQUlBLGdDQUFnQztJQUNoQyxNQUFNYyxtQkFBbUI7UUFDdkI7WUFBRUMsTUFBTTtZQUFNQyxPQUFPO1FBQVU7UUFDL0I7WUFBRUQsTUFBTTtZQUFNQyxPQUFPO1FBQVU7UUFDL0I7WUFBRUQsTUFBTTtZQUFNQyxPQUFPO1FBQVU7UUFDL0I7WUFBRUQsTUFBTTtZQUFNQyxPQUFPO1FBQVU7UUFDL0I7WUFBRUQsTUFBTTtZQUFNQyxPQUFPO1FBQVU7UUFDL0I7WUFBRUQsTUFBTTtZQUFNQyxPQUFPO1FBQVU7S0FDaEM7SUFFRCxNQUFNQyxrQkFBa0I7UUFDdEIsTUFBTUMsY0FBY0osaUJBQWlCSyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVMLElBQUksS0FBSzlCO1FBQzFELE9BQU9pQyxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFGLEtBQUssS0FBSTtJQUMvQjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNSyx5QkFBeUIsQ0FBQ0M7UUFDOUJ4QyxlQUFleUMsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1EO2FBQVc7UUFDNUNFLGNBQWM7ZUFBSTNDO1lBQWF5QztTQUFXO0lBQzVDO0lBRUEsTUFBTUcseUJBQXlCLENBQUNDLElBQVlDO1FBQzFDN0MsZUFBZXlDLENBQUFBLE9BQVFBLEtBQUtLLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFDOUJBLElBQUlILEVBQUUsS0FBS0EsS0FBSztvQkFBRSxHQUFHRyxHQUFHO29CQUFFLEdBQUdGLE9BQU87Z0JBQUMsSUFBSUU7SUFFN0M7SUFFQSxNQUFNQyx5QkFBeUIsQ0FBQ0o7UUFDOUI1QyxlQUFleUMsQ0FBQUEsT0FBUUEsS0FBS1EsTUFBTSxDQUFDRixDQUFBQSxNQUFPQSxJQUFJSCxFQUFFLEtBQUtBO1FBQ3JEMUMsc0JBQXNCO1FBQ3RCd0MsY0FBYzNDLFlBQVlrRCxNQUFNLENBQUNGLENBQUFBLE1BQU9BLElBQUlILEVBQUUsS0FBS0E7SUFDckQ7SUFFQSxNQUFNTSxtQ0FBbUMsQ0FBQ047UUFDeEMsK0NBQStDO1FBQy9DMUMsc0JBQXNCRCx1QkFBdUIyQyxLQUFLLE9BQU9BO0lBQzNEO0lBRUEsTUFBTUYsZ0JBQWdCLENBQUNTO1FBQ3JCLE1BQU1DLG9CQUFvQkQsa0JBQWtCcEQ7UUFDNUMsTUFBTXNELGFBQWE1QyxRQUFRNkMsS0FBSyxDQUFDLEdBQUczQyxlQUFlO1FBQ25EMEMsV0FBV0UsSUFBSSxDQUFDO2VBQUlIO1NBQWtCO1FBQ3RDMUMsV0FBVzJDO1FBQ1h6QyxnQkFBZ0J5QyxXQUFXRyxNQUFNLEdBQUc7SUFDdEM7SUFFQSxNQUFNQyxhQUFhO1FBQ2pCLElBQUk5QyxlQUFlLEdBQUc7WUFDcEJDLGdCQUFnQkQsZUFBZTtZQUMvQlgsZUFBZTttQkFBSVMsT0FBTyxDQUFDRSxlQUFlLEVBQUU7YUFBQztRQUMvQztJQUNGO0lBRUEsTUFBTStDLGFBQWE7UUFDakIsSUFBSS9DLGVBQWVGLFFBQVErQyxNQUFNLEdBQUcsR0FBRztZQUNyQzVDLGdCQUFnQkQsZUFBZTtZQUMvQlgsZUFBZTttQkFBSVMsT0FBTyxDQUFDRSxlQUFlLEVBQUU7YUFBQztRQUMvQztJQUNGO0lBRUEsTUFBTWdELGNBQWM7UUFDbEIsSUFBSUMsT0FBT0MsT0FBTyxDQUFDLGdCQUFnQjtZQUNqQzdELGVBQWUsRUFBRTtZQUNqQkUsc0JBQXNCO1lBQ3RCd0MsY0FBYyxFQUFFO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNb0IsYUFBYTtRQUNqQiw4QkFBOEI7UUFDOUJoQyxRQUFRaUMsR0FBRyxDQUFDLHVCQUF1QmhFO1FBQ25DaUUsTUFBTSxPQUEwQixPQUFuQmpFLFlBQVl5RCxNQUFNLEVBQUM7UUFDaENyQyxPQUFPb0MsSUFBSSxDQUFDbkUscURBQU1BLENBQUM2RSxXQUFXO0lBQ2hDO0lBRUEscUJBQXFCO0lBQ3JCLElBQUloRCxTQUFTO1FBQ1gscUJBQ0UsOERBQUNpRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSw4Q0FBOEM7SUFDOUMsSUFBSXBELHFCQUFxQixDQUFDRixlQUFlO1FBQ3ZDLHFCQUNFLDhEQUFDcUQ7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUNDRixXQUFVO2dDQUNWRyxTQUFTLElBQU1uRCxPQUFPb0QsSUFBSTswQ0FFMUIsNEVBQUNsRiwyRkFBU0E7b0NBQUM4RSxXQUFVOzs7Ozs7Ozs7OzswQ0FFdkIsOERBQUNEOztrREFDQyw4REFBQ007d0NBQUdMLFdBQVU7a0RBQW1DOzs7Ozs7a0RBQ2pELDhEQUFDQzt3Q0FBRUQsV0FBVTtrREFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU14Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNwRixxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7O2tEQUNULDhEQUFDQywwREFBU0E7a0RBQUM7Ozs7OztrREFDWCw4REFBQ0YsZ0VBQWVBO2tEQUFDOzs7Ozs7Ozs7Ozs7MENBRW5CLDhEQUFDRCw0REFBV0E7MENBQ1YsNEVBQUNrRjtvQ0FBSUMsV0FBVTs4Q0FDWjt3Q0FDQzs0Q0FBRXZCLElBQUk7NENBQVdYLE1BQU07NENBQVF3QyxLQUFLO3dDQUFvRjt3Q0FDeEg7NENBQUU3QixJQUFJOzRDQUFXWCxNQUFNOzRDQUFRd0MsS0FBSzt3Q0FBb0Y7d0NBQ3hIOzRDQUFFN0IsSUFBSTs0Q0FBV1gsTUFBTTs0Q0FBUXdDLEtBQUs7d0NBQWlGO3dDQUNySDs0Q0FBRTdCLElBQUk7NENBQVdYLE1BQU07NENBQVF3QyxLQUFLO3dDQUFvRjt3Q0FDeEg7NENBQUU3QixJQUFJOzRDQUFXWCxNQUFNOzRDQUFRd0MsS0FBSzt3Q0FBb0Y7cUNBQ3pILENBQUMzQixHQUFHLENBQUMsQ0FBQzRCLG9CQUNMLDhEQUFDUjs0Q0FFQ0MsV0FBVTs0Q0FDVkcsU0FBUztnREFDUHhELGlCQUFpQjtvREFDZjhCLElBQUk4QixJQUFJOUIsRUFBRTtvREFDVlgsTUFBTXlDLElBQUl6QyxJQUFJO29EQUNkd0MsS0FBS0MsSUFBSUQsR0FBRztvREFDWkUsT0FBTztvREFDUEMsUUFBUTtvREFDUkMsTUFBTTtvREFDTkMsUUFBUTtvREFDUnZELFdBQVc7b0RBQ1h3RCxZQUFZLElBQUlDO29EQUNoQkMsTUFBTSxFQUFFO2dEQUNWO2dEQUNBakUscUJBQXFCOzRDQUN2Qjs7OERBRUEsOERBQUNrRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ087d0RBQ0NRLEtBQUtSLElBQUlELEdBQUc7d0RBQ1pVLEtBQUtULElBQUl6QyxJQUFJO3dEQUNia0MsV0FBVTs7Ozs7Ozs7Ozs7OERBR2QsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFBSUMsV0FBVTtrRUFBdUJPLElBQUl6QyxJQUFJOzs7Ozs7Ozs7Ozs7MkNBMUIzQ3lDLElBQUk5QixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQW9DN0I7SUFFQSxxQkFDRSw4REFBQ3NCO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQ0NGLFdBQVU7Z0NBQ1ZHLFNBQVMsSUFBTW5ELE9BQU9vRCxJQUFJOzBDQUUxQiw0RUFBQ2xGLDJGQUFTQTtvQ0FBQzhFLFdBQVU7Ozs7Ozs7Ozs7OzBDQUV2Qiw4REFBQ0Q7O2tEQUNDLDhEQUFDTTt3Q0FBR0wsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUNDO3dDQUFFRCxXQUFVO2tEQUFzQnRELGNBQWNvQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR3pELDhEQUFDaUM7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNFOzRCQUNDRixXQUFVOzRCQUNWRyxTQUFTLElBQU10RCxxQkFBcUI7OzhDQUVwQyw4REFBQ3pCLDRGQUFTQTtvQ0FBQzRFLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNNUMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDMUUsd0ZBQWlCQTtnQ0FDaEJJLGFBQWFBO2dDQUNidUYsY0FBY3RGO2dDQUNkSyxjQUFjQTtnQ0FDZGtGLGVBQWVqRjtnQ0FDZjRCLGtCQUFrQkE7Z0NBQ2xCM0IsTUFBTUE7Z0NBQ05pRixjQUFjaEY7Z0NBQ2RDLFlBQVlBO2dDQUNaZ0Ysb0JBQW9CLElBQU0vRSxjQUFjLENBQUNEO2dDQUN6Q2lGLFNBQVM3RSxlQUFlO2dDQUN4QjhFLFNBQVM5RSxlQUFlRixRQUFRK0MsTUFBTSxHQUFHO2dDQUN6Q2tDLFFBQVFqQztnQ0FDUmtDLFFBQVFqQztnQ0FDUmtDLFFBQVE5QjtnQ0FDUitCLFNBQVNsQzs7Ozs7OzBDQUdYLDhEQUFDakUsa0ZBQWNBO2dDQUNiSyxhQUFhQTtnQ0FDYkUsb0JBQW9CQTtnQ0FDcEI2RixvQkFBb0I1RjtnQ0FDcEI2RixvQkFBb0IvQztnQ0FDcEJnRCxvQkFBb0JyRDtnQ0FDcEJzRCw4QkFBOEIvQzs7Ozs7Ozs7Ozs7O2tDQUtsQyw4REFBQ2dCO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDcEYscURBQUlBOzs4Q0FDSCw4REFBQ0csMkRBQVVBOztzREFDVCw4REFBQ0MsMERBQVNBO3NEQUFDOzs7Ozs7c0RBQ1gsOERBQUNGLGdFQUFlQTs7Z0RBQUM7Z0RBQ1NZLGdCQUFnQixXQUFXLE9BQU9BLGdCQUFnQixjQUFjLE9BQU9BLGdCQUFnQixXQUFXLE9BQU9BLGdCQUFnQixVQUFVLFFBQVE7Ozs7Ozs7Ozs7Ozs7OENBR3ZLLDhEQUFDYiw0REFBV0E7O3NEQUNWLDhEQUFDa0Y7NENBQ0NDLFdBQVU7NENBQ1YrQixPQUFPO2dEQUFFQyxXQUFXOzRDQUFRO3NEQUU1Qiw0RUFBQ2pDO2dEQUFJZ0MsT0FBTztvREFBRUUsV0FBVyxTQUFjLE9BQUwvRixNQUFLO29EQUFJZ0csaUJBQWlCO2dEQUFXOzBEQUNyRSw0RUFBQzdHLGlHQUFxQkE7b0RBQ3BCOEcsVUFBVXpGLGNBQWM0RCxHQUFHO29EQUMzQjhCLFlBQVkxRixjQUFjOEQsS0FBSztvREFDL0I2QixhQUFhM0YsY0FBYytELE1BQU07b0RBQ2pDN0UsYUFBYUE7b0RBQ2JFLG9CQUFvQkE7b0RBQ3BCSixhQUFhQTtvREFDYk0sY0FBY0E7b0RBQ2RzRyxjQUFjdEU7b0RBQ2Q5QixNQUFNQTtvREFDTkUsWUFBWUE7b0RBQ1ptRyxvQkFBb0JuRTtvREFDcEJ5RCxvQkFBb0JyRDtvREFDcEJtRCxvQkFBb0I1Rjs7Ozs7Ozs7Ozs7Ozs7OztzREFLMUIsOERBQUNnRTs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUN3QztvREFBR3hDLFdBQVU7OERBQXlDOzs7Ozs7OERBQ3ZELDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNDO3NFQUFFOzs7Ozs7c0VBQ0gsOERBQUNBO3NFQUFFOzs7Ozs7c0VBQ0gsOERBQUNBO3NFQUFFOzs7Ozs7c0VBQ0gsOERBQUNBO3NFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS1AsOERBQUNGOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBb0NwRSxZQUFZeUQsTUFBTTs7Ozs7O3NFQUNyRSw4REFBQ1U7NERBQUlDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBRXpDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFvQ3BFLFlBQVlrRCxNQUFNLENBQUMyRCxDQUFBQSxJQUFLQSxFQUFFQyxLQUFLLEVBQUVyRCxNQUFNOzs7Ozs7c0VBQzFGLDhEQUFDVTs0REFBSUMsV0FBVTtzRUFBd0I7Ozs7Ozs7Ozs7Ozs4REFFekMsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7O2dFQUFvQzJDLEtBQUtDLEtBQUssQ0FBQzFHLE9BQU87Z0VBQUs7Ozs7Ozs7c0VBQzFFLDhEQUFDNkQ7NERBQUlDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBRXpDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFvQzFELFFBQVErQyxNQUFNOzs7Ozs7c0VBQ2pFLDhEQUFDVTs0REFBSUMsV0FBVTtzRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3pEO0dBblh3QnZFOztRQWFQZixzREFBU0E7UUFDSEMsNERBQWVBOzs7S0FkZGMiLCJzb3VyY2VzIjpbIkY6XFxkZWVwc2lnaHRcXGZyb25ldGVuZFxcYXBwXFxhbm5vdGF0aW9uc1xcaW1hZ2UtYW5ub3RhdGVcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgUk9VVEVTIH0gZnJvbSAnQC9jb25zdGFudHMvcm91dGVzJ1xuaW1wb3J0IHsgQXJyb3dMZWZ0LCBJbWFnZSBhcyBJbWFnZUljb24gfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBJbWFnZUFubm90YXRpb25DYW52YXMsIEFubm90YXRpb25TaGFwZSB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvYW5ub3RhdGlvbi9pbWFnZS1hbm5vdGF0aW9uLWNhbnZhcydcbmltcG9ydCB7IEFubm90YXRpb25Ub29sYmFyLCBBbm5vdGF0aW9uVG9vbCB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvYW5ub3RhdGlvbi9hbm5vdGF0aW9uLXRvb2xiYXInXG5pbXBvcnQgeyBBbm5vdGF0aW9uTGlzdCB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvYW5ub3RhdGlvbi9hbm5vdGF0aW9uLWxpc3QnXG5pbXBvcnQgeyBJbWFnZURhdGEgfSBmcm9tICdAL3NlcnZpY2VzL21vY2tEYXRhJ1xuaW1wb3J0IHsgaW1hZ2VTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9pbWFnZVNlcnZpY2UnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEltYWdlQW5ub3RhdGVQYWdlKCkge1xuICBjb25zdCBbY3VycmVudFRvb2wsIHNldEN1cnJlbnRUb29sXSA9IHVzZVN0YXRlPEFubm90YXRpb25Ub29sPignc2VsZWN0JylcbiAgY29uc3QgW2Fubm90YXRpb25zLCBzZXRBbm5vdGF0aW9uc10gPSB1c2VTdGF0ZTxBbm5vdGF0aW9uU2hhcGVbXT4oW10pXG4gIGNvbnN0IFtzZWxlY3RlZEFubm90YXRpb24sIHNldFNlbGVjdGVkQW5ub3RhdGlvbl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbY3VycmVudExhYmVsLCBzZXRDdXJyZW50TGFiZWxdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFt6b29tLCBzZXRab29tXSA9IHVzZVN0YXRlKDEpXG4gIGNvbnN0IFtzaG93TGFiZWxzLCBzZXRTaG93TGFiZWxzXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtoaXN0b3J5LCBzZXRIaXN0b3J5XSA9IHVzZVN0YXRlPEFubm90YXRpb25TaGFwZVtdW10+KFtdKVxuICBjb25zdCBbaGlzdG9yeUluZGV4LCBzZXRIaXN0b3J5SW5kZXhdID0gdXNlU3RhdGUoLTEpXG4gIGNvbnN0IFtzZWxlY3RlZEltYWdlLCBzZXRTZWxlY3RlZEltYWdlXSA9IHVzZVN0YXRlPEltYWdlRGF0YSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzaG93SW1hZ2VTZWxlY3Rvciwgc2V0U2hvd0ltYWdlU2VsZWN0b3JdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcblxuICAvLyBMb2FkIGltYWdlIGZyb20gVVJMIHBhcmFtcyBvciBzaG93IHNlbGVjdG9yXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2ltYWdlSWQnKVxuICAgIGNvbnN0IGRhdGFzZXRJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2RhdGFzZXRJZCcpXG5cbiAgICBpZiAoaW1hZ2VJZCkge1xuICAgICAgbG9hZEltYWdlQnlJZChpbWFnZUlkKVxuICAgIH0gZWxzZSBpZiAoZGF0YXNldElkKSB7XG4gICAgICBsb2FkUmFuZG9tSW1hZ2VGcm9tRGF0YXNldChkYXRhc2V0SWQpXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIExvYWQgYSBkZWZhdWx0IGltYWdlIGZvciBpbW1lZGlhdGUgdXNlXG4gICAgICBsb2FkRGVmYXVsdEltYWdlKClcbiAgICB9XG4gIH0sIFtzZWFyY2hQYXJhbXNdKVxuXG4gIGNvbnN0IGxvYWREZWZhdWx0SW1hZ2UgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKVxuICAgIHRyeSB7XG4gICAgICAvLyBMb2FkIHRoZSBmaXJzdCBhdmFpbGFibGUgaW1hZ2UgYXMgZGVmYXVsdFxuICAgICAgY29uc3QgaW1hZ2UgPSBhd2FpdCBpbWFnZVNlcnZpY2UuZ2V0UmFuZG9tSW1hZ2UoKVxuICAgICAgaWYgKGltYWdlKSB7XG4gICAgICAgIHNldFNlbGVjdGVkSW1hZ2UoaW1hZ2UpXG4gICAgICAgIHNldFNob3dJbWFnZVNlbGVjdG9yKGZhbHNlKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gSWYgbm8gaW1hZ2VzIGF2YWlsYWJsZSwgc2hvdyBzZWxlY3RvclxuICAgICAgICBzZXRTaG93SW1hZ2VTZWxlY3Rvcih0cnVlKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBkZWZhdWx0IGltYWdlOicsIGVycm9yKVxuICAgICAgc2V0U2hvd0ltYWdlU2VsZWN0b3IodHJ1ZSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBsb2FkSW1hZ2VCeUlkID0gYXN5bmMgKGltYWdlSWQ6IHN0cmluZykgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgY29uc3QgaW1hZ2UgPSBhd2FpdCBpbWFnZVNlcnZpY2UuZ2V0SW1hZ2UoaW1hZ2VJZClcbiAgICAgIGlmIChpbWFnZSkge1xuICAgICAgICBzZXRTZWxlY3RlZEltYWdlKGltYWdlKVxuICAgICAgICBzZXRTaG93SW1hZ2VTZWxlY3RvcihmYWxzZSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgaW1hZ2U6JywgZXJyb3IpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbG9hZFJhbmRvbUltYWdlRnJvbURhdGFzZXQgPSBhc3luYyAoZGF0YXNldElkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGltYWdlID0gYXdhaXQgaW1hZ2VTZXJ2aWNlLmdldFJhbmRvbUltYWdlKGRhdGFzZXRJZClcbiAgICAgIGlmIChpbWFnZSkge1xuICAgICAgICBzZXRTZWxlY3RlZEltYWdlKGltYWdlKVxuICAgICAgICBzZXRTaG93SW1hZ2VTZWxlY3RvcihmYWxzZSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgcmFuZG9tIGltYWdlOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG5cblxuICAvLyBQcmVkZWZpbmVkIGxhYmVscyB3aXRoIGNvbG9yc1xuICBjb25zdCBwcmVkZWZpbmVkTGFiZWxzID0gW1xuICAgIHsgbmFtZTogJ+S6uueJqScsIGNvbG9yOiAnI2VmNDQ0NCcgfSxcbiAgICB7IG5hbWU6ICfovabovoYnLCBjb2xvcjogJyMzYjgyZjYnIH0sXG4gICAgeyBuYW1lOiAn5bu6562RJywgY29sb3I6ICcjMTBiOTgxJyB9LFxuICAgIHsgbmFtZTogJ+WKqOeJqScsIGNvbG9yOiAnI2Y1OWUwYicgfSxcbiAgICB7IG5hbWU6ICfmpI3niaknLCBjb2xvcjogJyM4YjVjZjYnIH0sXG4gICAgeyBuYW1lOiAn54mp5L2TJywgY29sb3I6ICcjZWM0ODk5JyB9LFxuICBdXG5cbiAgY29uc3QgZ2V0Q3VycmVudENvbG9yID0gKCkgPT4ge1xuICAgIGNvbnN0IGxhYmVsQ29uZmlnID0gcHJlZGVmaW5lZExhYmVscy5maW5kKGwgPT4gbC5uYW1lID09PSBjdXJyZW50TGFiZWwpXG4gICAgcmV0dXJuIGxhYmVsQ29uZmlnPy5jb2xvciB8fCAnI2VmNDQ0NCdcbiAgfVxuXG4gIC8vIEV2ZW50IGhhbmRsZXJzXG4gIGNvbnN0IGhhbmRsZUFubm90YXRpb25DcmVhdGUgPSAoYW5ub3RhdGlvbjogQW5ub3RhdGlvblNoYXBlKSA9PiB7XG4gICAgc2V0QW5ub3RhdGlvbnMocHJldiA9PiBbLi4ucHJldiwgYW5ub3RhdGlvbl0pXG4gICAgc2F2ZVRvSGlzdG9yeShbLi4uYW5ub3RhdGlvbnMsIGFubm90YXRpb25dKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQW5ub3RhdGlvblVwZGF0ZSA9IChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPEFubm90YXRpb25TaGFwZT4pID0+IHtcbiAgICBzZXRBbm5vdGF0aW9ucyhwcmV2ID0+IHByZXYubWFwKGFubiA9PlxuICAgICAgYW5uLmlkID09PSBpZCA/IHsgLi4uYW5uLCAuLi51cGRhdGVzIH0gOiBhbm5cbiAgICApKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQW5ub3RhdGlvbkRlbGV0ZSA9IChpZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0QW5ub3RhdGlvbnMocHJldiA9PiBwcmV2LmZpbHRlcihhbm4gPT4gYW5uLmlkICE9PSBpZCkpXG4gICAgc2V0U2VsZWN0ZWRBbm5vdGF0aW9uKG51bGwpXG4gICAgc2F2ZVRvSGlzdG9yeShhbm5vdGF0aW9ucy5maWx0ZXIoYW5uID0+IGFubi5pZCAhPT0gaWQpKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQW5ub3RhdGlvblZpc2liaWxpdHlUb2dnbGUgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIC8vIEZvciBub3csIGp1c3Qgc2VsZWN0L2Rlc2VsZWN0IHRoZSBhbm5vdGF0aW9uXG4gICAgc2V0U2VsZWN0ZWRBbm5vdGF0aW9uKHNlbGVjdGVkQW5ub3RhdGlvbiA9PT0gaWQgPyBudWxsIDogaWQpXG4gIH1cblxuICBjb25zdCBzYXZlVG9IaXN0b3J5ID0gKG5ld0Fubm90YXRpb25zPzogQW5ub3RhdGlvblNoYXBlW10pID0+IHtcbiAgICBjb25zdCBhbm5vdGF0aW9uc1RvU2F2ZSA9IG5ld0Fubm90YXRpb25zIHx8IGFubm90YXRpb25zXG4gICAgY29uc3QgbmV3SGlzdG9yeSA9IGhpc3Rvcnkuc2xpY2UoMCwgaGlzdG9yeUluZGV4ICsgMSlcbiAgICBuZXdIaXN0b3J5LnB1c2goWy4uLmFubm90YXRpb25zVG9TYXZlXSlcbiAgICBzZXRIaXN0b3J5KG5ld0hpc3RvcnkpXG4gICAgc2V0SGlzdG9yeUluZGV4KG5ld0hpc3RvcnkubGVuZ3RoIC0gMSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVVuZG8gPSAoKSA9PiB7XG4gICAgaWYgKGhpc3RvcnlJbmRleCA+IDApIHtcbiAgICAgIHNldEhpc3RvcnlJbmRleChoaXN0b3J5SW5kZXggLSAxKVxuICAgICAgc2V0QW5ub3RhdGlvbnMoWy4uLmhpc3RvcnlbaGlzdG9yeUluZGV4IC0gMV1dKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVJlZG8gPSAoKSA9PiB7XG4gICAgaWYgKGhpc3RvcnlJbmRleCA8IGhpc3RvcnkubGVuZ3RoIC0gMSkge1xuICAgICAgc2V0SGlzdG9yeUluZGV4KGhpc3RvcnlJbmRleCArIDEpXG4gICAgICBzZXRBbm5vdGF0aW9ucyhbLi4uaGlzdG9yeVtoaXN0b3J5SW5kZXggKyAxXV0pXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQ2xlYXIgPSAoKSA9PiB7XG4gICAgaWYgKHdpbmRvdy5jb25maXJtKCfnoa7lrpropoHmuIXnqbrmiYDmnInmoIfms6jlkJfvvJ8nKSkge1xuICAgICAgc2V0QW5ub3RhdGlvbnMoW10pXG4gICAgICBzZXRTZWxlY3RlZEFubm90YXRpb24obnVsbClcbiAgICAgIHNhdmVUb0hpc3RvcnkoW10pXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2F2ZSA9ICgpID0+IHtcbiAgICAvLyBTYXZlIGFubm90YXRpb25zIGxvZ2ljIGhlcmVcbiAgICBjb25zb2xlLmxvZygnU2F2aW5nIGFubm90YXRpb25zOicsIGFubm90YXRpb25zKVxuICAgIGFsZXJ0KGDlt7Lkv53lrZggJHthbm5vdGF0aW9ucy5sZW5ndGh9IOS4quagh+azqGApXG4gICAgcm91dGVyLnB1c2goUk9VVEVTLkFOTk9UQVRJT05TKVxuICB9XG5cbiAgLy8gU2hvdyBsb2FkaW5nIHN0YXRlXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+5q2j5Zyo5Yqg6L295Zu+5YOPLi4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIFNob3cgaW1hZ2Ugc2VsZWN0b3IgaWYgbm8gaW1hZ2UgaXMgc2VsZWN0ZWRcbiAgaWYgKHNob3dJbWFnZVNlbGVjdG9yIHx8ICFzZWxlY3RlZEltYWdlKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtMTAgdy0xMCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5iYWNrKCl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPuWbvuWDj+agh+azqDwvaDE+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMlwiPumAieaLqeimgeagh+azqOeahOWbvuWDjzwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2ltcGxlIEltYWdlIFNlbGVjdG9yICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZT7pgInmi6nlm77lg4/ov5vooYzmoIfms6g8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj7ngrnlh7vkuIvmlrnlm77lg4/lvIDlp4vmoIfms6g8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgICAgIHsgaWQ6ICdpbWdfMDAxJywgbmFtZTogJ+eMq+WSquWbvueJhycsIHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTE0ODg4Mjg2OTc0LTZjMDNlMmNhMWRiYT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfSxcbiAgICAgICAgICAgICAgICAgIHsgaWQ6ICdpbWdfMDAyJywgbmFtZTogJ+ihl+aZr+WbvueJhycsIHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDQ5ODI0OTEzOTM1LTU5YTEwYjhkMjAwMD93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfSxcbiAgICAgICAgICAgICAgICAgIHsgaWQ6ICdpbWdfMDAzJywgbmFtZTogJ+eLl+eLl+WbvueJhycsIHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTUyMDUzODMxLTcxNTk0YTI3NjMyZD93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfSxcbiAgICAgICAgICAgICAgICAgIHsgaWQ6ICdpbWdfMDA0JywgbmFtZTogJ+W7uuetkeWbvueJhycsIHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDg2NDA2MTQ2OTI2LWM2MjdhOTJhZDFhYj93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfSxcbiAgICAgICAgICAgICAgICAgIHsgaWQ6ICdpbWdfMDA1JywgbmFtZTogJ+mjjuaZr+WbvueJhycsIHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTA2OTA1OTI1MzQ2LTIxYmRhNGQzMmRmND93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfVxuICAgICAgICAgICAgICAgIF0ubWFwKChpbWcpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtpbWcuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIHJvdW5kZWQtbGcgYm9yZGVyLTIgYm9yZGVyLWdyYXktMjAwIGhvdmVyOmJvcmRlci1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZEltYWdlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBpbWcuaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBpbWcubmFtZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHVybDogaW1nLnVybCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiA4MDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDYwMCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6IDIwMDAwMCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvcm1hdDogJ2pwZycsXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhc2V0SWQ6ICcxJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHVwbG9hZGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICAgICAgICAgICAgICB0YWdzOiBbXVxuICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0ltYWdlU2VsZWN0b3IoZmFsc2UpXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXNwZWN0LXZpZGVvIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLXQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2ltZy51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e2ltZy5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPntpbWcubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC0xMCB3LTEwIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5iYWNrKCl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+5Zu+5YOP5qCH5rOoPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMlwiPntzZWxlY3RlZEltYWdlLm5hbWV9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC05IHJvdW5kZWQtbWQgcHgtMyBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQgdGV4dC1zbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93SW1hZ2VTZWxlY3Rvcih0cnVlKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICDliIfmjaLlm77lg49cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgIHsvKiBMZWZ0IFNpZGViYXIgLSBUb29scyBhbmQgQW5ub3RhdGlvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgPEFubm90YXRpb25Ub29sYmFyXG4gICAgICAgICAgICBjdXJyZW50VG9vbD17Y3VycmVudFRvb2x9XG4gICAgICAgICAgICBvblRvb2xDaGFuZ2U9e3NldEN1cnJlbnRUb29sfVxuICAgICAgICAgICAgY3VycmVudExhYmVsPXtjdXJyZW50TGFiZWx9XG4gICAgICAgICAgICBvbkxhYmVsQ2hhbmdlPXtzZXRDdXJyZW50TGFiZWx9XG4gICAgICAgICAgICBwcmVkZWZpbmVkTGFiZWxzPXtwcmVkZWZpbmVkTGFiZWxzfVxuICAgICAgICAgICAgem9vbT17em9vbX1cbiAgICAgICAgICAgIG9uWm9vbUNoYW5nZT17c2V0Wm9vbX1cbiAgICAgICAgICAgIHNob3dMYWJlbHM9e3Nob3dMYWJlbHN9XG4gICAgICAgICAgICBvblNob3dMYWJlbHNUb2dnbGU9eygpID0+IHNldFNob3dMYWJlbHMoIXNob3dMYWJlbHMpfVxuICAgICAgICAgICAgY2FuVW5kbz17aGlzdG9yeUluZGV4ID4gMH1cbiAgICAgICAgICAgIGNhblJlZG89e2hpc3RvcnlJbmRleCA8IGhpc3RvcnkubGVuZ3RoIC0gMX1cbiAgICAgICAgICAgIG9uVW5kbz17aGFuZGxlVW5kb31cbiAgICAgICAgICAgIG9uUmVkbz17aGFuZGxlUmVkb31cbiAgICAgICAgICAgIG9uU2F2ZT17aGFuZGxlU2F2ZX1cbiAgICAgICAgICAgIG9uQ2xlYXI9e2hhbmRsZUNsZWFyfVxuICAgICAgICAgIC8+XG5cbiAgICAgICAgICA8QW5ub3RhdGlvbkxpc3RcbiAgICAgICAgICAgIGFubm90YXRpb25zPXthbm5vdGF0aW9uc31cbiAgICAgICAgICAgIHNlbGVjdGVkQW5ub3RhdGlvbj17c2VsZWN0ZWRBbm5vdGF0aW9ufVxuICAgICAgICAgICAgb25Bbm5vdGF0aW9uU2VsZWN0PXtzZXRTZWxlY3RlZEFubm90YXRpb259XG4gICAgICAgICAgICBvbkFubm90YXRpb25EZWxldGU9e2hhbmRsZUFubm90YXRpb25EZWxldGV9XG4gICAgICAgICAgICBvbkFubm90YXRpb25VcGRhdGU9e2hhbmRsZUFubm90YXRpb25VcGRhdGV9XG4gICAgICAgICAgICBvbkFubm90YXRpb25WaXNpYmlsaXR5VG9nZ2xlPXtoYW5kbGVBbm5vdGF0aW9uVmlzaWJpbGl0eVRvZ2dsZX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTWFpbiBDYW52YXMgQXJlYSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0zXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZT7lm77lg4/moIfms6jljLrln588L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICDkvb/nlKjlt6bkvqflt6XlhbflnKjlm77lg4/kuIrliJvlu7rmoIfms6ggLSDlvZPliY3lt6Xlhbc6IHtjdXJyZW50VG9vbCA9PT0gJ3NlbGVjdCcgPyAn6YCJ5oupJyA6IGN1cnJlbnRUb29sID09PSAncmVjdGFuZ2xlJyA/ICfnn6nlvaInIDogY3VycmVudFRvb2wgPT09ICdjaXJjbGUnID8gJ+WchuW9oicgOiBjdXJyZW50VG9vbCA9PT0gJ3BvaW50JyA/ICfngrnmoIfms6gnIDogJ+Wkmui+ueW9oid9XG4gICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgb3ZlcmZsb3ctYXV0byBib3JkZXIgcm91bmRlZC1sZyBiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBtYXhIZWlnaHQ6ICc2MDBweCcgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgdHJhbnNmb3JtOiBgc2NhbGUoJHt6b29tfSlgLCB0cmFuc2Zvcm1PcmlnaW46ICd0b3AgbGVmdCcgfX0+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VBbm5vdGF0aW9uQ2FudmFzXG4gICAgICAgICAgICAgICAgICAgIGltYWdlVXJsPXtzZWxlY3RlZEltYWdlLnVybH1cbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VXaWR0aD17c2VsZWN0ZWRJbWFnZS53aWR0aH1cbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VIZWlnaHQ9e3NlbGVjdGVkSW1hZ2UuaGVpZ2h0fVxuICAgICAgICAgICAgICAgICAgICBhbm5vdGF0aW9ucz17YW5ub3RhdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQW5ub3RhdGlvbj17c2VsZWN0ZWRBbm5vdGF0aW9ufVxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50VG9vbD17Y3VycmVudFRvb2x9XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRMYWJlbD17Y3VycmVudExhYmVsfVxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q29sb3I9e2dldEN1cnJlbnRDb2xvcigpfVxuICAgICAgICAgICAgICAgICAgICB6b29tPXt6b29tfVxuICAgICAgICAgICAgICAgICAgICBzaG93TGFiZWxzPXtzaG93TGFiZWxzfVxuICAgICAgICAgICAgICAgICAgICBvbkFubm90YXRpb25DcmVhdGU9e2hhbmRsZUFubm90YXRpb25DcmVhdGV9XG4gICAgICAgICAgICAgICAgICAgIG9uQW5ub3RhdGlvblVwZGF0ZT17aGFuZGxlQW5ub3RhdGlvblVwZGF0ZX1cbiAgICAgICAgICAgICAgICAgICAgb25Bbm5vdGF0aW9uU2VsZWN0PXtzZXRTZWxlY3RlZEFubm90YXRpb259XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcC0zIGJnLWJsdWUtNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDAgbWItMlwiPuS9v+eUqOivtOaYjjo8L2g0PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtODAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIOmAieaLqeW3peWFt+WSjOagh+etvuWQju+8jOWcqOWbvuWDj+S4iuaLluaLveWIm+W7uuagh+azqDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwPuKAoiDngrnlh7vmoIfms6jlj6/ku6XpgInkuK3vvIzlnKjlj7PkvqfliJfooajkuK3nvJbovpHmoIfnrb48L3A+XG4gICAgICAgICAgICAgICAgICA8cD7igKIg5L2/55So57yp5pS+5bel5YW35Y+v5Lul5pS+5aSn5p+l55yL57uG6IqCPC9wPlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIOaUr+aMgeaSpOmUgC/ph43lgZrmk43kvZzvvIzlj6/ku6Xpmo/ml7bmgaLlpI3kuYvliY3nmoTnirbmgIE8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBTdGF0aXN0aWNzICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPnthbm5vdGF0aW9ucy5sZW5ndGh9PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPuaAu+agh+azqOaVsDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPnthbm5vdGF0aW9ucy5maWx0ZXIoYSA9PiBhLmxhYmVsKS5sZW5ndGh9PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPuW3suagh+iusDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntNYXRoLnJvdW5kKHpvb20gKiAxMDApfSU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+57yp5pS+5q+U5L6LPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e2hpc3RvcnkubGVuZ3RofTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7ljoblj7LorrDlvZU8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlU2VhcmNoUGFyYW1zIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlJPVVRFUyIsIkFycm93TGVmdCIsIkltYWdlIiwiSW1hZ2VJY29uIiwiSW1hZ2VBbm5vdGF0aW9uQ2FudmFzIiwiQW5ub3RhdGlvblRvb2xiYXIiLCJBbm5vdGF0aW9uTGlzdCIsImltYWdlU2VydmljZSIsIkltYWdlQW5ub3RhdGVQYWdlIiwiY3VycmVudFRvb2wiLCJzZXRDdXJyZW50VG9vbCIsImFubm90YXRpb25zIiwic2V0QW5ub3RhdGlvbnMiLCJzZWxlY3RlZEFubm90YXRpb24iLCJzZXRTZWxlY3RlZEFubm90YXRpb24iLCJjdXJyZW50TGFiZWwiLCJzZXRDdXJyZW50TGFiZWwiLCJ6b29tIiwic2V0Wm9vbSIsInNob3dMYWJlbHMiLCJzZXRTaG93TGFiZWxzIiwiaGlzdG9yeSIsInNldEhpc3RvcnkiLCJoaXN0b3J5SW5kZXgiLCJzZXRIaXN0b3J5SW5kZXgiLCJzZWxlY3RlZEltYWdlIiwic2V0U2VsZWN0ZWRJbWFnZSIsInNob3dJbWFnZVNlbGVjdG9yIiwic2V0U2hvd0ltYWdlU2VsZWN0b3IiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJvdXRlciIsInNlYXJjaFBhcmFtcyIsImltYWdlSWQiLCJnZXQiLCJkYXRhc2V0SWQiLCJsb2FkSW1hZ2VCeUlkIiwibG9hZFJhbmRvbUltYWdlRnJvbURhdGFzZXQiLCJsb2FkRGVmYXVsdEltYWdlIiwiaW1hZ2UiLCJnZXRSYW5kb21JbWFnZSIsImVycm9yIiwiY29uc29sZSIsImdldEltYWdlIiwicHJlZGVmaW5lZExhYmVscyIsIm5hbWUiLCJjb2xvciIsImdldEN1cnJlbnRDb2xvciIsImxhYmVsQ29uZmlnIiwiZmluZCIsImwiLCJoYW5kbGVBbm5vdGF0aW9uQ3JlYXRlIiwiYW5ub3RhdGlvbiIsInByZXYiLCJzYXZlVG9IaXN0b3J5IiwiaGFuZGxlQW5ub3RhdGlvblVwZGF0ZSIsImlkIiwidXBkYXRlcyIsIm1hcCIsImFubiIsImhhbmRsZUFubm90YXRpb25EZWxldGUiLCJmaWx0ZXIiLCJoYW5kbGVBbm5vdGF0aW9uVmlzaWJpbGl0eVRvZ2dsZSIsIm5ld0Fubm90YXRpb25zIiwiYW5ub3RhdGlvbnNUb1NhdmUiLCJuZXdIaXN0b3J5Iiwic2xpY2UiLCJwdXNoIiwibGVuZ3RoIiwiaGFuZGxlVW5kbyIsImhhbmRsZVJlZG8iLCJoYW5kbGVDbGVhciIsIndpbmRvdyIsImNvbmZpcm0iLCJoYW5kbGVTYXZlIiwibG9nIiwiYWxlcnQiLCJBTk5PVEFUSU9OUyIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwiYmFjayIsImgxIiwidXJsIiwiaW1nIiwid2lkdGgiLCJoZWlnaHQiLCJzaXplIiwiZm9ybWF0IiwidXBsb2FkZWRBdCIsIkRhdGUiLCJ0YWdzIiwic3JjIiwiYWx0Iiwib25Ub29sQ2hhbmdlIiwib25MYWJlbENoYW5nZSIsIm9uWm9vbUNoYW5nZSIsIm9uU2hvd0xhYmVsc1RvZ2dsZSIsImNhblVuZG8iLCJjYW5SZWRvIiwib25VbmRvIiwib25SZWRvIiwib25TYXZlIiwib25DbGVhciIsIm9uQW5ub3RhdGlvblNlbGVjdCIsIm9uQW5ub3RhdGlvbkRlbGV0ZSIsIm9uQW5ub3RhdGlvblVwZGF0ZSIsIm9uQW5ub3RhdGlvblZpc2liaWxpdHlUb2dnbGUiLCJzdHlsZSIsIm1heEhlaWdodCIsInRyYW5zZm9ybSIsInRyYW5zZm9ybU9yaWdpbiIsImltYWdlVXJsIiwiaW1hZ2VXaWR0aCIsImltYWdlSGVpZ2h0IiwiY3VycmVudENvbG9yIiwib25Bbm5vdGF0aW9uQ3JlYXRlIiwiaDQiLCJhIiwibGFiZWwiLCJNYXRoIiwicm91bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/annotations/image-annotate/page.tsx\n"));

/***/ })

});