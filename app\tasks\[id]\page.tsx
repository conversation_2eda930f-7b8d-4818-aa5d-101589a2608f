'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ROUTES } from '@/constants/routes'
import { ArrowLeft, Edit, User, Calendar, Clock, CheckCircle } from 'lucide-react'
import { mockTasks, getProjectById, getUserById, getDatasetById } from '@/services/mockData'

interface TaskDetailProps {
  params: Promise<{
    id: string
  }>
}

export default function TaskDetailPage({ params }: TaskDetailProps) {
  const [task, setTask] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    // Await params and load task data
    params.then(({ id }) => {
      const taskData = mockTasks.find(t => t.id === id)
      setTask(taskData)
    })
  }, [params])

  if (!task) {
    return <div className="flex items-center justify-center min-h-screen">加载中...</div>
  }

  const project = getProjectById(task.projectId)
  const dataset = getDatasetById(task.datasetId)
  const assignees = task.assignedTo.map((userId: string) => getUserById(userId)).filter(Boolean)

  const statusColors = {
    PENDING: 'bg-gray-100 text-gray-800',
    IN_PROGRESS: 'bg-blue-100 text-blue-800',
    REVIEW: 'bg-yellow-100 text-yellow-800',
    COMPLETED: 'bg-green-100 text-green-800',
    CANCELLED: 'bg-red-100 text-red-800',
  }

  const statusLabels = {
    PENDING: '待开始',
    IN_PROGRESS: '进行中',
    REVIEW: '审核中',
    COMPLETED: '已完成',
    CANCELLED: '已取消',
  }

  const typeLabels = {
    CLASSIFICATION: '分类',
    DETECTION: '检测',
    SEGMENTATION: '分割',
    TRANSCRIPTION: '转录',
    CUSTOM: '自定义',
  }

  const getProgress = () => {
    switch (task.status) {
      case 'COMPLETED': return 100
      case 'IN_PROGRESS': return Math.floor(Math.random() * 80) + 20
      case 'REVIEW': return 90
      default: return 0
    }
  }

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{task.name}</h1>
            <p className="text-gray-600 mt-2">{task.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Task Info */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>任务信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium text-gray-700">任务状态</span>
                  <div className="mt-1">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        statusColors[task.status as keyof typeof statusColors]
                      }`}
                    >
                      {statusLabels[task.status as keyof typeof statusLabels]}
                    </span>
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-700">任务类型</span>
                  <div className="mt-1 text-sm text-gray-900">
                    {typeLabels[task.type as keyof typeof typeLabels]}
                  </div>
                </div>
              </div>

              <div>
                <span className="text-sm font-medium text-gray-700">进度</span>
                <div className="mt-2">
                  <div className="flex justify-between text-sm mb-1">
                    <span>完成度</span>
                    <span>{getProgress()}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${getProgress()}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  创建时间：{task.createdAt.toLocaleDateString()}
                </div>
                <div className={`flex items-center text-sm ${
                  isOverdue && task.status !== 'COMPLETED' 
                    ? 'text-red-500' 
                    : 'text-gray-500'
                }`}>
                  <Clock className="h-4 w-4 mr-2" />
                  截止时间：{task.dueDate?.toLocaleDateString()}
                  {isOverdue && task.status !== 'COMPLETED' && (
                    <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                      已逾期
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>关联信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-700">关联项目：</span>
                <span className="text-sm text-gray-900 ml-2">{project?.name}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">关联数据集：</span>
                <span className="text-sm text-gray-900 ml-2">{dataset?.name}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">数据量：</span>
                <span className="text-sm text-gray-900 ml-2">
                  {dataset?.size.toLocaleString()} 个文件
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Assignees */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>
                <User className="inline h-5 w-5 mr-2" />
                负责人员
              </CardTitle>
              <CardDescription>
                分配给此任务的团队成员
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {assignees.map((user: any) => (
                  <div key={user.id} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      {user.username.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{user.username}</p>
                      <p className="text-xs text-gray-500">{user.email}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>任务统计</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">总进度</span>
                <span className="text-sm font-medium">{getProgress()}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">负责人数</span>
                <span className="text-sm font-medium">{assignees.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">数据量</span>
                <span className="text-sm font-medium">
                  {dataset?.size.toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
