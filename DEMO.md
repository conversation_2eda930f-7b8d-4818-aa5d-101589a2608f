# 深眸 AI 智能数据标注平台 - 演示指南

## 🚀 快速开始

### 1. 启动应用
```bash
npm run dev
```
应用将在 http://localhost:3001 启动

### 2. 登录系统
访问登录页面，使用以下测试账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 系统管理员 | 拥有所有权限 |
| zhangsan | password | 标注员 | 负责数据标注 |
| lisi | password | 项目管理员 | 管理项目和任务 |
| wangwu | password | 标注员 | 负责数据标注 |

## 📋 功能演示

### 🏠 仪表板
登录后首先看到仪表板，显示：
- **统计卡片**：项目数量、活跃任务、完成标注、团队成员
- **最近项目**：显示最新的3个项目及其状态
- **待办任务**：显示需要处理的任务列表

### 📁 项目管理

#### 查看项目列表
- 导航到 "项目管理" → "项目列表"
- 可以看到所有项目的卡片视图
- 支持搜索功能
- 每个项目显示：名称、描述、状态、成员数量、更新时间

#### 创建新项目
1. 点击 "创建项目" 按钮
2. 填写项目信息：
   - 项目名称（必填）
   - 项目描述
   - 项目类型（图像分类、目标检测等）
3. 点击 "创建项目" 完成

#### 查看项目详情
- 点击任意项目的 "查看详情" 按钮
- 可以看到：
  - 项目基本信息和统计数据
  - 团队成员列表
  - 最近任务
  - 关联的数据集

### 📊 数据集管理

#### 查看数据集列表
- 导航到 "数据集管理" → "数据集列表"
- 显示所有数据集的信息：
  - 数据集名称和描述
  - 数据类型（图像、音频、文本等）
  - 文件数量
  - 处理状态
  - 关联项目

#### 创建数据集
1. 点击 "创建数据集" 按钮
2. 填写基本信息：
   - 数据集名称
   - 描述
   - 数据类型
   - 关联项目
3. 上传数据文件（支持拖拽）
4. 点击 "创建数据集"

### 🤖 模型管理

#### 查看模型列表
- 导航到 "模型管理" → "模型列表"
- 显示所有AI模型：
  - 模型名称和描述
  - 模型类型
  - 版本号
  - 训练状态
  - 准确率指标

#### 创建模型
1. 点击 "创建模型" 按钮
2. 配置模型信息：
   - 基本信息（名称、描述、类型）
   - 技术配置（框架、架构）
   - 超参数设置
   - 上传预训练模型文件
3. 点击 "创建模型"

### 🏷️ 标注管理

#### 查看标注列表
- 导航到 "标注管理" → "标注列表"
- 显示所有标注记录：
  - 数据项名称
  - 关联任务
  - 标注员
  - 标注状态
  - 置信度
  - 标签信息

#### 创建标注
1. 点击 "创建标注" 按钮
2. 选择任务和数据项
3. 添加标签
4. 设置置信度
5. 添加备注（可选）
6. 右侧预览区域显示数据项

### ✅ 任务管理

#### 查看任务列表
- 导航到 "任务管理" → "任务列表"
- 显示所有任务：
  - 任务名称和描述
  - 任务类型
  - 执行状态
  - 进度条
  - 负责人
  - 截止日期

#### 创建任务
1. 点击 "创建任务" 按钮
2. 填写任务信息：
   - 基本信息（名称、描述、类型）
   - 关联项目和数据集
   - 设置优先级和截止日期
   - 分配团队成员
3. 点击 "创建任务"

### 👤 个人中心

#### 查看个人资料
- 导航到 "个人中心"
- 显示：
  - 个人基本信息
  - 活动统计
  - 最近活动记录

#### 编辑资料
1. 点击 "编辑资料" 按钮
2. 修改个人信息
3. 点击 "保存更改"

## 🎯 核心特性

### 🔍 搜索功能
- 所有列表页面都支持实时搜索
- 支持按名称、描述等字段搜索

### 📱 响应式设计
- 支持桌面、平板、手机等不同设备
- 自适应布局，确保最佳用户体验

### 🎨 现代化UI
- 基于 shadcn/ui 组件库
- 一致的设计语言
- 流畅的交互动画

### 📊 数据可视化
- 统计卡片显示关键指标
- 进度条显示任务完成情况
- 状态标签清晰标识各种状态

### 🔐 权限管理
- 基于角色的访问控制
- 不同用户看到不同的功能
- 安全的登录认证

## 🛠️ 技术特性

### 前端技术栈
- **Next.js 15** - React 全栈框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 原子化CSS
- **shadcn/ui** - 现代化组件库
- **Lucide React** - 图标库

### 数据管理
- **模拟数据服务** - 完整的数据模型
- **状态管理** - Zustand 全局状态
- **本地存储** - 用户认证状态

### 开发体验
- **热重载** - 实时开发反馈
- **ESLint** - 代码质量检查
- **TypeScript** - 完整类型定义

## 🎮 使用建议

1. **从仪表板开始** - 了解整体概况
2. **创建项目** - 建立工作空间
3. **上传数据集** - 准备标注数据
4. **创建任务** - 分配标注工作
5. **进行标注** - 完成数据标注
6. **训练模型** - 使用标注数据

## 🔄 数据流程

```
项目创建 → 数据集上传 → 任务分配 → 数据标注 → 质量审核 → 模型训练
```

## 📝 注意事项

- 当前为演示版本，使用模拟数据
- 所有操作都是前端模拟，不会真正保存到后端
- 刷新页面后某些状态可能重置
- 建议使用现代浏览器获得最佳体验

## 🚀 下一步

这个演示平台展示了完整的AI数据标注工作流程。在实际部署时，需要：

1. 集成真实的后端API
2. 实现文件上传和存储
3. 添加实时协作功能
4. 集成AI模型训练
5. 添加更多数据类型支持

---

**深眸 AI 智能数据标注平台** - 让数据标注更智能、更高效！
