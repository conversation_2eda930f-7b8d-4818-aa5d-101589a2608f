"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-selector.tsx":
/*!**************************************************!*\
  !*** ./components/annotation/image-selector.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageSelector: () => (/* binding */ ImageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ ImageSelector auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImageSelector(param) {\n    let { onImageSelect, selectedImageId, datasetId } = param;\n    _s();\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFormat, setSelectedFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageSelector.useEffect\": ()=>{\n            loadImages();\n        }\n    }[\"ImageSelector.useEffect\"], [\n        datasetId,\n        selectedFormat,\n        selectedTags\n    ]);\n    const loadImages = async ()=>{\n        setLoading(true);\n        try {\n            const params = {\n                datasetId,\n                format: selectedFormat === 'all' ? undefined : selectedFormat,\n                tags: selectedTags.length > 0 ? selectedTags : undefined\n            };\n            console.log('Loading images with params:', params);\n            const imageList = await _services_imageService__WEBPACK_IMPORTED_MODULE_6__.imageService.getImages(params);\n            console.log('Loaded images:', imageList);\n            setImages(imageList);\n        } catch (error) {\n            console.error('Failed to load images:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Filter images by search term\n    const filteredImages = images.filter((image)=>{\n        var _image_tags;\n        return image.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_image_tags = image.tags) === null || _image_tags === void 0 ? void 0 : _image_tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    });\n    // Get unique formats and tags\n    const availableFormats = [\n        ...new Set(images.map((img)=>img.format))\n    ];\n    const availableTags = [\n        ...new Set(images.flatMap((img)=>img.tags || []))\n    ];\n    const handleTagToggle = (tag)=>{\n        setSelectedTags((prev)=>prev.includes(tag) ? prev.filter((t)=>t !== tag) : [\n                ...prev,\n                tag\n            ]);\n    };\n    const handleRandomSelect = async ()=>{\n        try {\n            const randomImage = await _services_imageService__WEBPACK_IMPORTED_MODULE_6__.imageService.getRandomImage(datasetId);\n            if (randomImage) {\n                onImageSelect(randomImage);\n            }\n        } catch (error) {\n            console.error('Failed to get random image:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"选择图像\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: \"正在加载图像列表...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-6 w-6 animate-spin text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            \"选择图像\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: [\n                            \"选择要进行标注的图像 (\",\n                            filteredImages.length,\n                            \" 张可用)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"搜索图像名称或标签...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: selectedFormat,\n                                        onValueChange: setSelectedFormat,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                    placeholder: \"格式\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"所有格式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    availableFormats.map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: format,\n                                                            children: format.toUpperCase()\n                                                        }, format, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleRandomSelect,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"随机选择\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            availableTags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"标签筛选:\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: availableTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-2 py-1 text-xs rounded-full border transition-colors cursor-pointer \".concat(selectedTags.includes(tag) ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'),\n                                                onClick: ()=>handleTagToggle(tag),\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3 max-h-96 overflow-y-auto\",\n                        children: filteredImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative cursor-pointer rounded-lg border-2 transition-all \".concat(selectedImageId === image.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'),\n                                onClick: ()=>onImageSelect(image),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: image.url,\n                                            alt: image.name,\n                                            className: \"w-full h-full object-cover\",\n                                            loading: \"lazy\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium truncate\",\n                                                children: image.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    image.width,\n                                                    \"\\xd7\",\n                                                    image.height,\n                                                    \" • \",\n                                                    (image.size / 1024).toFixed(1),\n                                                    \"KB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            image.tags && image.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                children: [\n                                                    image.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded\",\n                                                            children: tag\n                                                        }, tag, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this)),\n                                                    image.tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded\",\n                                                        children: [\n                                                            \"+\",\n                                                            image.tags.length - 2\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, image.id, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    filteredImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-2 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"没有找到匹配的图像\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"尝试调整搜索条件或筛选器\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageSelector, \"cT+WXUdh5uud5stShw9g4Jm8hrg=\");\n_c = ImageSelector;\nvar _c;\n$RefreshReg$(_c, \"ImageSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-selector.tsx\n"));

/***/ })

});