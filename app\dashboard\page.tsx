'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  mockProjects,
  mockTasks,
  mockAnnotations,
  mockUsers,
  getTasksByUserId,
  getAnnotationsByUserId
} from '@/services/mockData'

export default function DashboardPage() {
  const [currentUser, setCurrentUser] = useState<string>('')
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeTasks: 0,
    completedAnnotations: 0,
    teamMembers: 0,
  })

  useEffect(() => {
    const username = localStorage.getItem('currentUser') || 'admin'
    setCurrentUser(username)

    // Calculate statistics
    const user = mockUsers.find(u => u.username === username)
    const userId = user?.id || '1'

    const userTasks = getTasksByUserId(userId)
    const userAnnotations = getAnnotationsByUserId(userId)

    setStats({
      totalProjects: mockProjects.length,
      activeTasks: userTasks.filter(task => task.status === 'IN_PROGRESS').length,
      completedAnnotations: userAnnotations.filter(ann => ann.status === 'APPROVED').length,
      teamMembers: mockUsers.length,
    })
  }, [])
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <p className="text-gray-600 mt-2">欢迎使用深眸 AI 智能数据标注平台</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总项目数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProjects}</div>
            <p className="text-xs text-muted-foreground">
              共 {stats.totalProjects} 个项目
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃任务</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeTasks}</div>
            <p className="text-xs text-muted-foreground">
              进行中的任务
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成标注</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedAnnotations}</div>
            <p className="text-xs text-muted-foreground">
              已完成标注
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">团队成员</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.teamMembers}</div>
            <p className="text-xs text-muted-foreground">
              团队成员总数
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>最近项目</CardTitle>
            <CardDescription>您最近访问的项目</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockProjects.slice(0, 3).map((project) => {
                const statusColors = {
                  ACTIVE: 'bg-green-100 text-green-800',
                  PAUSED: 'bg-yellow-100 text-yellow-800',
                  COMPLETED: 'bg-gray-100 text-gray-800',
                  ARCHIVED: 'bg-red-100 text-red-800',
                }
                const statusLabels = {
                  ACTIVE: '进行中',
                  PAUSED: '暂停',
                  COMPLETED: '已完成',
                  ARCHIVED: '已归档',
                }

                return (
                  <div key={project.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{project.name}</p>
                      <p className="text-sm text-gray-500">
                        更新于 {project.updatedAt.toLocaleDateString()}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${statusColors[project.status]}`}>
                      {statusLabels[project.status]}
                    </span>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>待办任务</CardTitle>
            <CardDescription>需要您处理的任务</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockTasks.filter(task => task.status !== 'COMPLETED').slice(0, 3).map((task) => {
                const isOverdue = task.dueDate && new Date(task.dueDate) < new Date()
                const priorityColors = {
                  urgent: 'bg-red-100 text-red-800',
                  high: 'bg-orange-100 text-orange-800',
                  medium: 'bg-yellow-100 text-yellow-800',
                  low: 'bg-green-100 text-green-800',
                }

                const getPriority = () => {
                  if (isOverdue) return 'urgent'
                  if (task.status === 'REVIEW') return 'high'
                  if (task.status === 'IN_PROGRESS') return 'medium'
                  return 'low'
                }

                const getPriorityLabel = () => {
                  if (isOverdue) return '已逾期'
                  if (task.status === 'REVIEW') return '审核中'
                  if (task.status === 'IN_PROGRESS') return '进行中'
                  return '待开始'
                }

                return (
                  <div key={task.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{task.name}</p>
                      <p className="text-sm text-gray-500">
                        截止日期：{task.dueDate?.toLocaleDateString()}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${priorityColors[getPriority() as keyof typeof priorityColors]}`}>
                      {getPriorityLabel()}
                    </span>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
