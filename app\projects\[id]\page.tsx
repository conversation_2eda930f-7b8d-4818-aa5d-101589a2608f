'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ROUTES } from '@/constants/routes'
import { ArrowLeft, Edit, Share, Download, Upload, Users, Settings } from 'lucide-react'
import {
  getProjectById,
  getUserById,
  getDatasetsByProjectId,
  getTasksByProjectId
} from '@/services/mockData'

interface ProjectDetailProps {
  params: Promise<{
    id: string
  }>
}

// Mock project data
const mockProject = {
  id: '1',
  name: '图像分类项目',
  description: '用于训练图像分类模型的数据标注项目，包含多种类别的图像数据，需要进行精确的分类标注。',
  status: 'active',
  type: 'classification',
  memberCount: 5,
  datasetCount: 3,
  taskCount: 12,
  completedTasks: 8,
  createdAt: '2024-01-15',
  updatedAt: '2024-01-20',
  createdBy: 'admin',
  members: [
    { id: '1', name: '张三', role: 'owner', avatar: '' },
    { id: '2', name: '李四', role: 'admin', avatar: '' },
    { id: '3', name: '王五', role: 'member', avatar: '' },
  ],
  datasets: [
    { id: '1', name: '训练数据集', size: 1000, status: 'ready' },
    { id: '2', name: '验证数据集', size: 200, status: 'ready' },
    { id: '3', name: '测试数据集', size: 300, status: 'processing' },
  ],
  tasks: [
    { id: '1', name: '图像分类标注', status: 'completed', assignee: '张三' },
    { id: '2', name: '质量检查', status: 'in_progress', assignee: '李四' },
    { id: '3', name: '数据验证', status: 'pending', assignee: '王五' },
  ]
}

export default function ProjectDetailPage({ params }: ProjectDetailProps) {
  const [project, setProject] = useState<any>(null)
  const [datasets, setDatasets] = useState<any[]>([])
  const [tasks, setTasks] = useState<any[]>([])
  const router = useRouter()

  useEffect(() => {
    // Await params and load project data
    params.then(({ id }) => {
      const projectData = getProjectById(id)
      if (projectData) {
        setProject(projectData)

        // Load related data
        const projectDatasets = getDatasetsByProjectId(id)
        const projectTasks = getTasksByProjectId(id)

        setDatasets(projectDatasets)
        setTasks(projectTasks)
      }
    })
  }, [params])

  const statusColors = {
    ACTIVE: 'bg-green-100 text-green-800',
    PAUSED: 'bg-yellow-100 text-yellow-800',
    COMPLETED: 'bg-gray-100 text-gray-800',
    ARCHIVED: 'bg-red-100 text-red-800',
  }

  const statusLabels = {
    ACTIVE: '进行中',
    PAUSED: '暂停',
    COMPLETED: '已完成',
    ARCHIVED: '已归档',
  }

  if (!project) {
    return <div className="flex items-center justify-center min-h-screen">加载中...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
            <p className="text-gray-600 mt-2">{project.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-2" />
            分享
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            导入
          </Button>
          <Button size="sm">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </div>
      </div>

      {/* Status and Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">项目状态</CardTitle>
          </CardHeader>
          <CardContent>
            <span
              className={`px-2 py-1 text-xs rounded-full ${
                statusColors[project.status as keyof typeof statusColors]
              }`}
            >
              {statusLabels[project.status as keyof typeof statusLabels]}
            </span>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">团队成员</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{project.members.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据集</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{datasets.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">任务进度</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tasks.filter(t => t.status === 'COMPLETED').length}/{tasks.length}
            </div>
            <p className="text-xs text-muted-foreground">
              {tasks.length > 0 ? Math.round((tasks.filter(t => t.status === 'COMPLETED').length / tasks.length) * 100) : 0}% 完成
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Content Tabs */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Team Members */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>团队成员</CardTitle>
              <CardDescription>项目成员列表</CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              管理成员
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {project.members.map((member: any) => {
                const user = getUserById(member.userId)
                return (
                  <div key={member.userId} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        {user?.username.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="font-medium">{user?.username}</p>
                        <p className="text-sm text-gray-500">{member.role}</p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Recent Tasks */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>最近任务</CardTitle>
              <CardDescription>项目中的任务列表</CardDescription>
            </div>
            <Button variant="outline" size="sm">
              查看全部
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {tasks.slice(0, 3).map((task) => {
                const assignees = task.assignedTo.map((userId: string) => getUserById(userId)?.username).filter(Boolean)
                const taskStatusColors = {
                  PENDING: 'bg-gray-100 text-gray-800',
                  IN_PROGRESS: 'bg-blue-100 text-blue-800',
                  REVIEW: 'bg-yellow-100 text-yellow-800',
                  COMPLETED: 'bg-green-100 text-green-800',
                  CANCELLED: 'bg-red-100 text-red-800',
                }
                const taskStatusLabels = {
                  PENDING: '待开始',
                  IN_PROGRESS: '进行中',
                  REVIEW: '审核中',
                  COMPLETED: '已完成',
                  CANCELLED: '已取消',
                }

                return (
                  <div key={task.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{task.name}</p>
                      <p className="text-sm text-gray-500">负责人：{assignees.join(', ')}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${taskStatusColors[task.status as keyof typeof taskStatusColors]}`}>
                      {taskStatusLabels[task.status as keyof typeof taskStatusLabels]}
                    </span>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Datasets */}
      <Card>
        <CardHeader>
          <CardTitle>数据集</CardTitle>
          <CardDescription>项目关联的数据集</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {datasets.map((dataset) => {
              const datasetStatusColors = {
                UPLOADING: 'bg-blue-100 text-blue-800',
                PROCESSING: 'bg-yellow-100 text-yellow-800',
                READY: 'bg-green-100 text-green-800',
                ERROR: 'bg-red-100 text-red-800',
              }
              const datasetStatusLabels = {
                UPLOADING: '上传中',
                PROCESSING: '处理中',
                READY: '就绪',
                ERROR: '错误',
              }

              return (
                <div key={dataset.id} className="border rounded-lg p-4">
                  <h4 className="font-medium">{dataset.name}</h4>
                  <p className="text-sm text-gray-500 mt-1">
                    {dataset.size.toLocaleString()} 个文件
                  </p>
                  <span className={`inline-block mt-2 px-2 py-1 text-xs rounded-full ${datasetStatusColors[dataset.status as keyof typeof datasetStatusColors]}`}>
                    {datasetStatusLabels[dataset.status as keyof typeof datasetStatusLabels]}
                  </span>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
