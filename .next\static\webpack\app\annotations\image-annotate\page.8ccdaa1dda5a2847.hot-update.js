"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./app/annotations/image-annotate/page.tsx":
/*!*************************************************!*\
  !*** ./app/annotations/image-annotate/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageAnnotatePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/routes */ \"(app-pages-browser)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/annotation/image-annotation-canvas */ \"(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\");\n/* harmony import */ var _components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/annotation/annotation-toolbar */ \"(app-pages-browser)/./components/annotation/annotation-toolbar.tsx\");\n/* harmony import */ var _components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/annotation/annotation-list */ \"(app-pages-browser)/./components/annotation/annotation-list.tsx\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ImageAnnotatePage() {\n    _s();\n    const [currentTool, setCurrentTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [annotations, setAnnotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnnotation, setSelectedAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentLabel, setCurrentLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showLabels, setShowLabels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Load image from URL params or show selector\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotatePage.useEffect\": ()=>{\n            const imageId = searchParams.get('imageId');\n            const datasetId = searchParams.get('datasetId');\n            if (imageId) {\n                loadImageById(imageId);\n            } else if (datasetId) {\n                loadRandomImageFromDataset(datasetId);\n            } else {\n                // Load a default image for immediate use\n                loadDefaultImage();\n            }\n        }\n    }[\"ImageAnnotatePage.useEffect\"], [\n        searchParams\n    ]);\n    const loadDefaultImage = async ()=>{\n        setLoading(true);\n        try {\n            // Load the first available image as default\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage();\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            } else {\n                // If no images available, show selector\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load default image:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadImageById = async (imageId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getImage(imageId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRandomImageFromDataset = async (datasetId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage(datasetId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load random image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Predefined labels with colors\n    const predefinedLabels = [\n        {\n            name: '人物',\n            color: '#ef4444'\n        },\n        {\n            name: '车辆',\n            color: '#3b82f6'\n        },\n        {\n            name: '建筑',\n            color: '#10b981'\n        },\n        {\n            name: '动物',\n            color: '#f59e0b'\n        },\n        {\n            name: '植物',\n            color: '#8b5cf6'\n        },\n        {\n            name: '物体',\n            color: '#ec4899'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    // Event handlers\n    const handleAnnotationCreate = (annotation)=>{\n        setAnnotations((prev)=>[\n                ...prev,\n                annotation\n            ]);\n        saveToHistory([\n            ...annotations,\n            annotation\n        ]);\n    };\n    const handleAnnotationUpdate = (id, updates)=>{\n        setAnnotations((prev)=>prev.map((ann)=>ann.id === id ? {\n                    ...ann,\n                    ...updates\n                } : ann));\n    };\n    const handleAnnotationDelete = (id)=>{\n        setAnnotations((prev)=>prev.filter((ann)=>ann.id !== id));\n        setSelectedAnnotation(null);\n        saveToHistory(annotations.filter((ann)=>ann.id !== id));\n    };\n    const handleAnnotationVisibilityToggle = (id)=>{\n        // For now, just select/deselect the annotation\n        setSelectedAnnotation(selectedAnnotation === id ? null : id);\n    };\n    const saveToHistory = (newAnnotations)=>{\n        const annotationsToSave = newAnnotations || annotations;\n        const newHistory = history.slice(0, historyIndex + 1);\n        newHistory.push([\n            ...annotationsToSave\n        ]);\n        setHistory(newHistory);\n        setHistoryIndex(newHistory.length - 1);\n    };\n    const handleUndo = ()=>{\n        if (historyIndex > 0) {\n            setHistoryIndex(historyIndex - 1);\n            setAnnotations([\n                ...history[historyIndex - 1]\n            ]);\n        }\n    };\n    const handleRedo = ()=>{\n        if (historyIndex < history.length - 1) {\n            setHistoryIndex(historyIndex + 1);\n            setAnnotations([\n                ...history[historyIndex + 1]\n            ]);\n        }\n    };\n    const handleClear = ()=>{\n        if (window.confirm('确定要清空所有标注吗？')) {\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n            saveToHistory([]);\n        }\n    };\n    const handleSave = ()=>{\n        // Save annotations logic here\n        console.log('Saving annotations:', annotations);\n        alert(\"已保存 \".concat(annotations.length, \" 个标注\"));\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.ANNOTATIONS);\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载图像...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Show image selector if no image is selected\n    if (showImageSelector || !selectedImage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"选择要标注的图像\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"选择图像进行标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"点击下方图像开始标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        {\n                                            id: 'img_001',\n                                            name: '猫咪图片',\n                                            url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_002',\n                                            name: '街景图片',\n                                            url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_003',\n                                            name: '狗狗图片',\n                                            url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_004',\n                                            name: '建筑图片',\n                                            url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_005',\n                                            name: '风景图片',\n                                            url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'\n                                        }\n                                    ].map((img)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"cursor-pointer rounded-lg border-2 border-gray-200 hover:border-blue-500 transition-colors\",\n                                            onClick: ()=>{\n                                                setSelectedImage({\n                                                    id: img.id,\n                                                    name: img.name,\n                                                    url: img.url,\n                                                    width: 800,\n                                                    height: 600,\n                                                    size: 200000,\n                                                    format: 'jpg',\n                                                    datasetId: '1',\n                                                    uploadedAt: new Date(),\n                                                    tags: []\n                                                });\n                                                setShowImageSelector(false);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: img.url,\n                                                        alt: img.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: img.name\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, img.id, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: selectedImage.name\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"inline-flex items-center justify-center h-9 rounded-md px-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground text-sm\",\n                            onClick: ()=>setShowImageSelector(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                \"切换图像\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__.AnnotationToolbar, {\n                                currentTool: currentTool,\n                                onToolChange: setCurrentTool,\n                                currentLabel: currentLabel,\n                                onLabelChange: setCurrentLabel,\n                                predefinedLabels: predefinedLabels,\n                                zoom: zoom,\n                                onZoomChange: setZoom,\n                                showLabels: showLabels,\n                                onShowLabelsToggle: ()=>setShowLabels(!showLabels),\n                                canUndo: historyIndex > 0,\n                                canRedo: historyIndex < history.length - 1,\n                                onUndo: handleUndo,\n                                onRedo: handleRedo,\n                                onSave: handleSave,\n                                onClear: handleClear\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__.AnnotationList, {\n                                annotations: annotations,\n                                selectedAnnotation: selectedAnnotation,\n                                onAnnotationSelect: setSelectedAnnotation,\n                                onAnnotationDelete: handleAnnotationDelete,\n                                onAnnotationUpdate: handleAnnotationUpdate,\n                                onAnnotationVisibilityToggle: handleAnnotationVisibilityToggle\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"图像标注区域\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                \"使用左侧工具在图像上创建标注 - 当前工具: \",\n                                                currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-auto border rounded-lg bg-gray-50\",\n                                            style: {\n                                                maxHeight: '600px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"scale(\".concat(zoom, \")\"),\n                                                    transformOrigin: 'top left'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__.ImageAnnotationCanvas, {\n                                                    imageUrl: selectedImage.url,\n                                                    imageWidth: selectedImage.width,\n                                                    imageHeight: selectedImage.height,\n                                                    annotations: annotations,\n                                                    selectedAnnotation: selectedAnnotation,\n                                                    currentTool: currentTool,\n                                                    currentLabel: currentLabel,\n                                                    currentColor: getCurrentColor(),\n                                                    zoom: zoom,\n                                                    showLabels: showLabels,\n                                                    onAnnotationCreate: handleAnnotationCreate,\n                                                    onAnnotationUpdate: handleAnnotationUpdate,\n                                                    onAnnotationSelect: setSelectedAnnotation\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"使用说明:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 选择工具和标签后，在图像上拖拽创建标注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 点击标注可以选中，在右侧列表中编辑标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 使用缩放工具可以放大查看细节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 支持撤销/重做操作，可以随时恢复之前的状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"总标注数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.filter((a)=>a.label).length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"已标记\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                Math.round(zoom * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"缩放比例\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: history.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"历史记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotatePage, \"9pHOkhHZxupwN/FxwGc1+4XtiAM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ImageAnnotatePage;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/annotations/image-annotate/page.tsx\n"));

/***/ })

});