'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Image as ImageIcon, Square, Circle, MapPin } from 'lucide-react'
import { getAllImages, ImageData } from '@/services/mockData'

export default function SimpleImageAnnotatePage() {
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null)
  const [currentTool, setCurrentTool] = useState<'select' | 'rectangle' | 'circle' | 'point'>('rectangle')
  const [annotations, setAnnotations] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    loadImage()
  }, [searchParams])

  const loadImage = async () => {
    try {
      const imageId = searchParams.get('imageId')
      const images = getAllImages()
      
      let imageToLoad = null
      if (imageId) {
        imageToLoad = images.find(img => img.id === imageId)
      }
      
      if (!imageToLoad && images.length > 0) {
        imageToLoad = images[0] // 使用第一张图片作为默认
      }
      
      if (imageToLoad) {
        setSelectedImage(imageToLoad)
      }
    } catch (error) {
      console.error('Failed to load image:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleImageSelect = (image: ImageData) => {
    setSelectedImage(image)
    setAnnotations([]) // 清空标注
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载图像...</p>
        </div>
      </div>
    )
  }

  const images = getAllImages()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            className="inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">图像标注</h1>
            <p className="text-gray-600 mt-2">
              {selectedImage ? selectedImage.name : '请选择图像'}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar - Image Selection and Tools */}
        <div className="space-y-4">
          {/* Image Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ImageIcon className="h-5 w-5 mr-2" />
                图像列表
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {images.map((image) => (
                  <div
                    key={image.id}
                    className={`p-2 border rounded cursor-pointer transition-colors ${
                      selectedImage?.id === image.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleImageSelect(image)}
                  >
                    <div className="text-sm font-medium truncate">{image.name}</div>
                    <div className="text-xs text-gray-500">
                      {image.width}×{image.height}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Tools */}
          <Card>
            <CardHeader>
              <CardTitle>标注工具</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {[
                  { id: 'rectangle', icon: Square, label: '矩形' },
                  { id: 'circle', icon: Circle, label: '圆形' },
                  { id: 'point', icon: MapPin, label: '点标注' }
                ].map((tool) => {
                  const Icon = tool.icon
                  return (
                    <button
                      key={tool.id}
                      className={`w-full flex items-center justify-center p-2 rounded border transition-colors ${
                        currentTool === tool.id
                          ? 'bg-blue-500 text-white border-blue-500'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                      onClick={() => setCurrentTool(tool.id as any)}
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      {tool.label}
                    </button>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Annotations List */}
          <Card>
            <CardHeader>
              <CardTitle>标注列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600">
                共 {annotations.length} 个标注
              </div>
              {annotations.length === 0 && (
                <p className="text-xs text-gray-500 mt-2">
                  在图像上拖拽创建标注
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Main Canvas Area */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>图像标注区域</CardTitle>
              <CardDescription>
                当前工具: {currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : '点标注'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedImage ? (
                <div className="space-y-4">
                  {/* Image Display */}
                  <div className="relative border rounded-lg bg-gray-50 overflow-hidden">
                    <img
                      src={selectedImage.url}
                      alt={selectedImage.name}
                      className="w-full h-auto max-h-96 object-contain"
                      onLoad={() => console.log('Image loaded successfully:', selectedImage.name)}
                      onError={(e) => console.error('Image failed to load:', e)}
                    />
                    {/* 这里可以添加标注画布覆盖层 */}
                    <div className="absolute inset-0 pointer-events-none">
                      {/* 标注会显示在这里 */}
                    </div>
                  </div>

                  {/* Image Info */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">尺寸:</span> {selectedImage.width}×{selectedImage.height}
                    </div>
                    <div>
                      <span className="font-medium">格式:</span> {selectedImage.format.toUpperCase()}
                    </div>
                    <div>
                      <span className="font-medium">大小:</span> {(selectedImage.size / 1024).toFixed(1)}KB
                    </div>
                    <div>
                      <span className="font-medium">数据集:</span> {selectedImage.datasetId}
                    </div>
                  </div>

                  {/* Tags */}
                  {selectedImage.tags && selectedImage.tags.length > 0 && (
                    <div>
                      <div className="font-medium text-sm mb-2">标签:</div>
                      <div className="flex flex-wrap gap-1">
                        {selectedImage.tags.map(tag => (
                          <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Instructions */}
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明:</h4>
                    <div className="text-sm text-blue-800 space-y-1">
                      <p>• 从左侧选择标注工具</p>
                      <p>• 在图像上拖拽创建标注</p>
                      <p>• 标注将显示在标注列表中</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <ImageIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>请从左侧选择一个图像开始标注</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">可用图像:</span> {images.length}</div>
            <div><span className="font-medium">当前选择:</span> {selectedImage?.id || '无'}</div>
            <div><span className="font-medium">当前工具:</span> {currentTool}</div>
            <div><span className="font-medium">标注数量:</span> {annotations.length}</div>
            {selectedImage && (
              <div><span className="font-medium">图像URL:</span> {selectedImage.url}</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
