import { User, Project, Dataset, Task, Annotation, Model } from '@/types/common';

// Mock Users
export const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    avatar: '',
    role: 'ADMIN' as any,
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    username: '<PERSON><PERSON><PERSON>',
    email: 'z<PERSON><PERSON>@deepsight.ai',
    avatar: '',
    role: 'ANNOTATOR' as any,
    createdAt: new Date('2023-02-01'),
    updatedAt: new Date('2024-01-18'),
  },
  {
    id: '3',
    username: 'lisi',
    email: '<EMAIL>',
    avatar: '',
    role: 'MANAGER' as any,
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: '4',
    username: 'wangwu',
    email: '<EMAIL>',
    avatar: '',
    role: 'ANNOTATOR' as any,
    createdAt: new Date('2023-04-01'),
    updatedAt: new Date('2024-01-10'),
  },
];

// Mock Projects
export const mockProjects: Project[] = [
  {
    id: '1',
    name: '图像分类项目',
    description: '用于训练图像分类模型的数据标注项目，包含10个类别的图像数据',
    status: 'ACTIVE' as any,
    createdBy: '1',
    members: [
      { userId: '1', role: 'OWNER' as any, joinedAt: new Date('2024-01-15') },
      { userId: '2', role: 'MEMBER' as any, joinedAt: new Date('2024-01-16') },
      { userId: '3', role: 'ADMIN' as any, joinedAt: new Date('2024-01-17') },
    ],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    name: '目标检测项目',
    description: '自动驾驶场景下的目标检测数据标注项目',
    status: 'ACTIVE' as any,
    createdBy: '1',
    members: [
      { userId: '1', role: 'OWNER' as any, joinedAt: new Date('2024-01-10') },
      { userId: '2', role: 'MEMBER' as any, joinedAt: new Date('2024-01-11') },
      { userId: '3', role: 'MEMBER' as any, joinedAt: new Date('2024-01-12') },
      { userId: '4', role: 'MEMBER' as any, joinedAt: new Date('2024-01-13') },
    ],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
  },
  {
    id: '3',
    name: '语音识别项目',
    description: '多语言语音识别数据集标注项目',
    status: 'COMPLETED' as any,
    createdBy: '3',
    members: [
      { userId: '3', role: 'OWNER' as any, joinedAt: new Date('2023-12-01') },
      { userId: '4', role: 'MEMBER' as any, joinedAt: new Date('2023-12-02') },
    ],
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-05'),
  },
  {
    id: '4',
    name: '文本分析项目',
    description: '自然语言处理和情感分析项目',
    status: 'PAUSED' as any,
    createdBy: '2',
    members: [
      { userId: '2', role: 'OWNER' as any, joinedAt: new Date('2024-01-05') },
      { userId: '1', role: 'ADMIN' as any, joinedAt: new Date('2024-01-06') },
    ],
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-12'),
  },
];

// Mock Datasets
export const mockDatasets: Dataset[] = [
  {
    id: '1',
    name: '图像分类数据集',
    description: '包含10个类别的图像数据，用于训练分类模型',
    type: 'IMAGE' as any,
    size: 10000,
    status: 'READY' as any,
    projectId: '1',
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    name: '目标检测数据集',
    description: '自动驾驶场景下的目标检测数据',
    type: 'IMAGE' as any,
    size: 5000,
    status: 'PROCESSING' as any,
    projectId: '2',
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
  },
  {
    id: '3',
    name: '语音识别数据集',
    description: '多语言语音数据集，包含中英文语音',
    type: 'AUDIO' as any,
    size: 2000,
    status: 'READY' as any,
    projectId: '3',
    createdBy: '3',
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-05'),
  },
  {
    id: '4',
    name: '文本情感数据集',
    description: '用于情感分析的文本数据集',
    type: 'TEXT' as any,
    size: 15000,
    status: 'READY' as any,
    projectId: '4',
    createdBy: '2',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-12'),
  },
  {
    id: '5',
    name: '视频动作识别数据集',
    description: '人体动作识别视频数据集',
    type: 'VIDEO' as any,
    size: 800,
    status: 'UPLOADING' as any,
    projectId: '1',
    createdBy: '1',
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-19'),
  },
];

// Mock Tasks
export const mockTasks: Task[] = [
  {
    id: '1',
    name: '图像分类标注',
    description: '对图像数据进行分类标注，包含10个类别',
    type: 'CLASSIFICATION' as any,
    status: 'IN_PROGRESS' as any,
    projectId: '1',
    datasetId: '1',
    assignedTo: ['2', '3'],
    createdBy: '1',
    dueDate: new Date('2024-01-25'),
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    name: '目标检测标注',
    description: '对自动驾驶场景进行目标检测标注',
    type: 'DETECTION' as any,
    status: 'PENDING' as any,
    projectId: '2',
    datasetId: '2',
    assignedTo: ['4'],
    createdBy: '1',
    dueDate: new Date('2024-01-30'),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
  },
  {
    id: '3',
    name: '语音转录任务',
    description: '将语音文件转录为文本',
    type: 'TRANSCRIPTION' as any,
    status: 'COMPLETED' as any,
    projectId: '3',
    datasetId: '3',
    assignedTo: ['4'],
    createdBy: '3',
    dueDate: new Date('2024-01-20'),
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-05'),
  },
  {
    id: '4',
    name: '情感分析标注',
    description: '对文本进行情感极性标注',
    type: 'CLASSIFICATION' as any,
    status: 'REVIEW' as any,
    projectId: '4',
    datasetId: '4',
    assignedTo: ['2'],
    createdBy: '2',
    dueDate: new Date('2024-01-28'),
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-12'),
  },
];

// Mock Annotations
export const mockAnnotations: Annotation[] = [
  {
    id: '1',
    taskId: '1',
    dataItemId: 'img_001',
    annotatorId: '2',
    data: {
      category: '猫',
      confidence: 0.95,
      boundingBox: null,
    },
    status: 'APPROVED' as any,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-21'),
  },
  {
    id: '2',
    taskId: '2',
    dataItemId: 'img_002',
    annotatorId: '3',
    data: {
      objects: [
        { class: '汽车', bbox: [100, 100, 200, 200], confidence: 0.88 },
        { class: '行人', bbox: [300, 150, 350, 300], confidence: 0.92 },
      ],
    },
    status: 'SUBMITTED' as any,
    createdAt: new Date('2024-01-19'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '3',
    taskId: '3',
    dataItemId: 'audio_001',
    annotatorId: '4',
    data: {
      transcription: '你好，欢迎使用深眸AI标注平台',
      language: 'zh-CN',
      confidence: 0.96,
    },
    status: 'APPROVED' as any,
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-19'),
  },
  {
    id: '4',
    taskId: '4',
    dataItemId: 'text_001',
    annotatorId: '2',
    data: {
      sentiment: 'positive',
      confidence: 0.87,
      keywords: ['优秀', '满意', '推荐'],
    },
    status: 'DRAFT' as any,
    createdAt: new Date('2024-01-17'),
    updatedAt: new Date('2024-01-18'),
  },
];

// Mock Models
export const mockModels: Model[] = [
  {
    id: '1',
    name: 'ResNet-50 分类模型',
    description: '基于ResNet-50架构的图像分类模型，支持10个类别',
    type: 'CLASSIFICATION' as any,
    version: 'v1.2.0',
    status: 'DEPLOYED' as any,
    projectId: '1',
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    name: 'YOLO-v8 检测模型',
    description: '用于自动驾驶场景的目标检测模型',
    type: 'DETECTION' as any,
    version: 'v2.1.0',
    status: 'TRAINING' as any,
    projectId: '2',
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
  },
  {
    id: '3',
    name: 'Whisper 语音识别',
    description: '多语言语音识别模型，支持中英文',
    type: 'NLP' as any,
    version: 'v1.0.0',
    status: 'READY' as any,
    projectId: '3',
    createdBy: '3',
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-05'),
  },
  {
    id: '4',
    name: 'BERT 情感分析模型',
    description: '基于BERT的中文情感分析模型',
    type: 'NLP' as any,
    version: 'v1.1.0',
    status: 'READY' as any,
    projectId: '4',
    createdBy: '2',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-12'),
  },
];

// Helper functions to get related data
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getProjectById = (id: string): Project | undefined => {
  return mockProjects.find(project => project.id === id);
};

export const getDatasetById = (id: string): Dataset | undefined => {
  return mockDatasets.find(dataset => dataset.id === id);
};

export const getTaskById = (id: string): Task | undefined => {
  return mockTasks.find(task => task.id === id);
};

export const getModelById = (id: string): Model | undefined => {
  return mockModels.find(model => model.id === id);
};

export const getProjectsByUserId = (userId: string): Project[] => {
  return mockProjects.filter(project => 
    project.createdBy === userId || 
    project.members.some(member => member.userId === userId)
  );
};

export const getDatasetsByProjectId = (projectId: string): Dataset[] => {
  return mockDatasets.filter(dataset => dataset.projectId === projectId);
};

export const getTasksByProjectId = (projectId: string): Task[] => {
  return mockTasks.filter(task => task.projectId === projectId);
};

export const getTasksByUserId = (userId: string): Task[] => {
  return mockTasks.filter(task => task.assignedTo.includes(userId));
};

export const getAnnotationsByTaskId = (taskId: string): Annotation[] => {
  return mockAnnotations.filter(annotation => annotation.taskId === taskId);
};

export const getAnnotationsByUserId = (userId: string): Annotation[] => {
  return mockAnnotations.filter(annotation => annotation.annotatorId === userId);
};
