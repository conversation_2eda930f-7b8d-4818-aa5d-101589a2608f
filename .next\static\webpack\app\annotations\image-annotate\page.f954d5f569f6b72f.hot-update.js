"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-selector.tsx":
/*!**************************************************!*\
  !*** ./components/annotation/image-selector.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageSelector: () => (/* binding */ ImageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ ImageSelector auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImageSelector(param) {\n    let { onImageSelect, selectedImageId, datasetId } = param;\n    _s();\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFormat, setSelectedFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageSelector.useEffect\": ()=>{\n            loadImages();\n        }\n    }[\"ImageSelector.useEffect\"], [\n        datasetId,\n        selectedFormat,\n        selectedTags\n    ]);\n    const loadImages = async ()=>{\n        setLoading(true);\n        try {\n            const params = {\n                datasetId,\n                format: selectedFormat === 'all' ? undefined : selectedFormat,\n                tags: selectedTags.length > 0 ? selectedTags : undefined\n            };\n            const imageList = await _services_imageService__WEBPACK_IMPORTED_MODULE_6__.imageService.getImages(params);\n            setImages(imageList);\n        } catch (error) {\n            console.error('Failed to load images:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Filter images by search term\n    const filteredImages = images.filter((image)=>{\n        var _image_tags;\n        return image.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_image_tags = image.tags) === null || _image_tags === void 0 ? void 0 : _image_tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    });\n    // Get unique formats and tags\n    const availableFormats = [\n        ...new Set(images.map((img)=>img.format))\n    ];\n    const availableTags = [\n        ...new Set(images.flatMap((img)=>img.tags || []))\n    ];\n    const handleTagToggle = (tag)=>{\n        setSelectedTags((prev)=>prev.includes(tag) ? prev.filter((t)=>t !== tag) : [\n                ...prev,\n                tag\n            ]);\n    };\n    const handleRandomSelect = async ()=>{\n        try {\n            const randomImage = await _services_imageService__WEBPACK_IMPORTED_MODULE_6__.imageService.getRandomImage(datasetId);\n            if (randomImage) {\n                onImageSelect(randomImage);\n            }\n        } catch (error) {\n            console.error('Failed to get random image:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"选择图像\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: \"正在加载图像列表...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-6 w-6 animate-spin text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            \"选择图像\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: [\n                            \"选择要进行标注的图像 (\",\n                            filteredImages.length,\n                            \" 张可用)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"搜索图像名称或标签...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: selectedFormat,\n                                        onValueChange: setSelectedFormat,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                    placeholder: \"格式\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"所有格式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    availableFormats.map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: format,\n                                                            children: format.toUpperCase()\n                                                        }, format, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleRandomSelect,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"随机选择\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            availableTags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"标签筛选:\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: availableTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-2 py-1 text-xs rounded-full border transition-colors cursor-pointer \".concat(selectedTags.includes(tag) ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'),\n                                                onClick: ()=>handleTagToggle(tag),\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3 max-h-96 overflow-y-auto\",\n                        children: filteredImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative cursor-pointer rounded-lg border-2 transition-all \".concat(selectedImageId === image.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'),\n                                onClick: ()=>onImageSelect(image),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: image.url,\n                                            alt: image.name,\n                                            className: \"w-full h-full object-cover\",\n                                            loading: \"lazy\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium truncate\",\n                                                children: image.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    image.width,\n                                                    \"\\xd7\",\n                                                    image.height,\n                                                    \" • \",\n                                                    (image.size / 1024).toFixed(1),\n                                                    \"KB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            image.tags && image.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                children: [\n                                                    image.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded\",\n                                                            children: tag\n                                                        }, tag, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this)),\n                                                    image.tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded\",\n                                                        children: [\n                                                            \"+\",\n                                                            image.tags.length - 2\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, image.id, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    filteredImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-2 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"没有找到匹配的图像\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"尝试调整搜索条件或筛选器\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageSelector, \"cT+WXUdh5uud5stShw9g4Jm8hrg=\");\n_c = ImageSelector;\nvar _c;\n$RefreshReg$(_c, \"ImageSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-selector.tsx\n"));

/***/ })

});