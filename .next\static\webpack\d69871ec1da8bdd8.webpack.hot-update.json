{"c": ["app/layout", "app/annotations/image-annotate/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./components/annotation/image-selector.tsx", "(app-pages-browser)/./components/ui/select.tsx", "(app-pages-browser)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "(app-pages-browser)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-arrow/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-popper/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-size/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_check_private_redeclaration.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_get.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_init.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_set.js", "(app-pages-browser)/./node_modules/aria-hidden/dist/es2015/index.js", "(app-pages-browser)/./node_modules/get-nonce/dist/es2015/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/Combination.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/UI.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/hook.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/singleton.js", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/assignRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useRef.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/exports.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/medium.js"]}