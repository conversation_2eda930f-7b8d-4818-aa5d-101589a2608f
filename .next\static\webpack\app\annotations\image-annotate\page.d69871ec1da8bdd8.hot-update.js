"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./app/annotations/image-annotate/page.tsx":
/*!*************************************************!*\
  !*** ./app/annotations/image-annotate/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageAnnotatePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/routes */ \"(app-pages-browser)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/annotation/image-annotation-canvas */ \"(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\");\n/* harmony import */ var _components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/annotation/annotation-toolbar */ \"(app-pages-browser)/./components/annotation/annotation-toolbar.tsx\");\n/* harmony import */ var _components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/annotation/annotation-list */ \"(app-pages-browser)/./components/annotation/annotation-list.tsx\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ImageAnnotatePage() {\n    _s();\n    const [currentTool, setCurrentTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [annotations, setAnnotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnnotation, setSelectedAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentLabel, setCurrentLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showLabels, setShowLabels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Load image from URL params or show selector\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotatePage.useEffect\": ()=>{\n            const imageId = searchParams.get('imageId');\n            const datasetId = searchParams.get('datasetId');\n            if (imageId) {\n                loadImageById(imageId);\n            } else if (datasetId) {\n                loadRandomImageFromDataset(datasetId);\n            } else {\n                // Load a default image for immediate use\n                loadDefaultImage();\n            }\n        }\n    }[\"ImageAnnotatePage.useEffect\"], [\n        searchParams\n    ]);\n    const loadDefaultImage = async ()=>{\n        setLoading(true);\n        try {\n            // Load the first available image as default\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_9__.imageService.getRandomImage();\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            } else {\n                // If no images available, show selector\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load default image:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadImageById = async (imageId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_9__.imageService.getImage(imageId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRandomImageFromDataset = async (datasetId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_9__.imageService.getRandomImage(datasetId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load random image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleImageSelect = (image)=>{\n        setSelectedImage(image);\n        setShowImageSelector(false);\n        // Clear existing annotations when switching images\n        setAnnotations([]);\n        setSelectedAnnotation(null);\n        setHistory([]);\n        setHistoryIndex(-1);\n    };\n    // Predefined labels with colors\n    const predefinedLabels = [\n        {\n            name: '人物',\n            color: '#ef4444'\n        },\n        {\n            name: '车辆',\n            color: '#3b82f6'\n        },\n        {\n            name: '建筑',\n            color: '#10b981'\n        },\n        {\n            name: '动物',\n            color: '#f59e0b'\n        },\n        {\n            name: '植物',\n            color: '#8b5cf6'\n        },\n        {\n            name: '物体',\n            color: '#ec4899'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    // Event handlers\n    const handleAnnotationCreate = (annotation)=>{\n        setAnnotations((prev)=>[\n                ...prev,\n                annotation\n            ]);\n        saveToHistory([\n            ...annotations,\n            annotation\n        ]);\n    };\n    const handleAnnotationUpdate = (id, updates)=>{\n        setAnnotations((prev)=>prev.map((ann)=>ann.id === id ? {\n                    ...ann,\n                    ...updates\n                } : ann));\n    };\n    const handleAnnotationDelete = (id)=>{\n        setAnnotations((prev)=>prev.filter((ann)=>ann.id !== id));\n        setSelectedAnnotation(null);\n        saveToHistory(annotations.filter((ann)=>ann.id !== id));\n    };\n    const handleAnnotationVisibilityToggle = (id)=>{\n        // For now, just select/deselect the annotation\n        setSelectedAnnotation(selectedAnnotation === id ? null : id);\n    };\n    const saveToHistory = (newAnnotations)=>{\n        const annotationsToSave = newAnnotations || annotations;\n        const newHistory = history.slice(0, historyIndex + 1);\n        newHistory.push([\n            ...annotationsToSave\n        ]);\n        setHistory(newHistory);\n        setHistoryIndex(newHistory.length - 1);\n    };\n    const handleUndo = ()=>{\n        if (historyIndex > 0) {\n            setHistoryIndex(historyIndex - 1);\n            setAnnotations([\n                ...history[historyIndex - 1]\n            ]);\n        }\n    };\n    const handleRedo = ()=>{\n        if (historyIndex < history.length - 1) {\n            setHistoryIndex(historyIndex + 1);\n            setAnnotations([\n                ...history[historyIndex + 1]\n            ]);\n        }\n    };\n    const handleClear = ()=>{\n        if (window.confirm('确定要清空所有标注吗？')) {\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n            saveToHistory([]);\n        }\n    };\n    const handleSave = ()=>{\n        // Save annotations logic here\n        console.log('Saving annotations:', annotations);\n        alert(\"已保存 \".concat(annotations.length, \" 个标注\"));\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_5__.ROUTES.ANNOTATIONS);\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载图像...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    // Show image selector if no image is selected\n    if (showImageSelector || !selectedImage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"选择要标注的图像\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"选择图像进行标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"点击下方图像开始标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        {\n                                            id: 'img_001',\n                                            name: '猫咪图片',\n                                            url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_002',\n                                            name: '街景图片',\n                                            url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_003',\n                                            name: '狗狗图片',\n                                            url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_004',\n                                            name: '建筑图片',\n                                            url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_005',\n                                            name: '风景图片',\n                                            url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'\n                                        }\n                                    ].map((img)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"cursor-pointer rounded-lg border-2 border-gray-200 hover:border-blue-500 transition-colors\",\n                                            onClick: ()=>{\n                                                setSelectedImage({\n                                                    id: img.id,\n                                                    name: img.name,\n                                                    url: img.url,\n                                                    width: 800,\n                                                    height: 600,\n                                                    size: 200000,\n                                                    format: 'jpg',\n                                                    datasetId: '1',\n                                                    uploadedAt: new Date(),\n                                                    tags: []\n                                                });\n                                                setShowImageSelector(false);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: img.url,\n                                                        alt: img.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: img.name\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, img.id, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: selectedImage.name\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>setShowImageSelector(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                \"切换图像\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_7__.AnnotationToolbar, {\n                                currentTool: currentTool,\n                                onToolChange: setCurrentTool,\n                                currentLabel: currentLabel,\n                                onLabelChange: setCurrentLabel,\n                                predefinedLabels: predefinedLabels,\n                                zoom: zoom,\n                                onZoomChange: setZoom,\n                                showLabels: showLabels,\n                                onShowLabelsToggle: ()=>setShowLabels(!showLabels),\n                                canUndo: historyIndex > 0,\n                                canRedo: historyIndex < history.length - 1,\n                                onUndo: handleUndo,\n                                onRedo: handleRedo,\n                                onSave: handleSave,\n                                onClear: handleClear\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_8__.AnnotationList, {\n                                annotations: annotations,\n                                selectedAnnotation: selectedAnnotation,\n                                onAnnotationSelect: setSelectedAnnotation,\n                                onAnnotationDelete: handleAnnotationDelete,\n                                onAnnotationUpdate: handleAnnotationUpdate,\n                                onAnnotationVisibilityToggle: handleAnnotationVisibilityToggle\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"图像标注区域\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: [\n                                                \"使用左侧工具在图像上创建标注 - 当前工具: \",\n                                                currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-auto border rounded-lg bg-gray-50\",\n                                            style: {\n                                                maxHeight: '600px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"scale(\".concat(zoom, \")\"),\n                                                    transformOrigin: 'top left'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_6__.ImageAnnotationCanvas, {\n                                                    imageUrl: selectedImage.url,\n                                                    imageWidth: selectedImage.width,\n                                                    imageHeight: selectedImage.height,\n                                                    annotations: annotations,\n                                                    selectedAnnotation: selectedAnnotation,\n                                                    currentTool: currentTool,\n                                                    currentLabel: currentLabel,\n                                                    currentColor: getCurrentColor(),\n                                                    zoom: zoom,\n                                                    showLabels: showLabels,\n                                                    onAnnotationCreate: handleAnnotationCreate,\n                                                    onAnnotationUpdate: handleAnnotationUpdate,\n                                                    onAnnotationSelect: setSelectedAnnotation\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"使用说明:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 选择工具和标签后，在图像上拖拽创建标注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 点击标注可以选中，在右侧列表中编辑标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 使用缩放工具可以放大查看细节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 支持撤销/重做操作，可以随时恢复之前的状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"总标注数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.filter((a)=>a.label).length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"已标记\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                Math.round(zoom * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"缩放比例\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: history.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"历史记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotatePage, \"9pHOkhHZxupwN/FxwGc1+4XtiAM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ImageAnnotatePage;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/annotations/image-annotate/page.tsx\n"));

/***/ })

});