'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ROUTES } from '@/constants/routes'
import { ArrowLeft, Edit, Cpu, Calendar, Activity, Download, Play, Pause } from 'lucide-react'
import { mockModels, getProjectById } from '@/services/mockData'

interface ModelDetailProps {
  params: Promise<{
    id: string
  }>
}

export default function ModelDetailPage({ params }: ModelDetailProps) {
  const [model, setModel] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    // Await params and load model data
    params.then(({ id }) => {
      const modelData = mockModels.find(m => m.id === id)
      setModel(modelData)
    })
  }, [params])

  if (!model) {
    return <div className="flex items-center justify-center min-h-screen">加载中...</div>
  }

  const project = getProjectById(model.projectId)

  const statusColors = {
    TRAINING: 'bg-blue-100 text-blue-800',
    READY: 'bg-green-100 text-green-800',
    DEPLOYED: 'bg-purple-100 text-purple-800',
    ERROR: 'bg-red-100 text-red-800',
  }

  const statusLabels = {
    TRAINING: '训练中',
    READY: '就绪',
    DEPLOYED: '已部署',
    ERROR: '错误',
  }

  const typeLabels = {
    CLASSIFICATION: '分类',
    DETECTION: '检测',
    SEGMENTATION: '分割',
    NLP: '自然语言',
    CUSTOM: '自定义',
  }

  // Mock performance metrics
  const metrics = {
    accuracy: Math.random() * 0.3 + 0.7, // 70-100%
    precision: Math.random() * 0.3 + 0.7,
    recall: Math.random() * 0.3 + 0.7,
    f1Score: Math.random() * 0.3 + 0.7,
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{model.name}</h1>
            <p className="text-gray-600 mt-2">{model.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            下载
          </Button>
          {model.status === 'DEPLOYED' ? (
            <Button variant="outline" size="sm">
              <Pause className="h-4 w-4 mr-2" />
              停止
            </Button>
          ) : (
            <Button variant="outline" size="sm">
              <Play className="h-4 w-4 mr-2" />
              部署
            </Button>
          )}
          <Button size="sm">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">模型状态</CardTitle>
          </CardHeader>
          <CardContent>
            <span
              className={`px-2 py-1 text-xs rounded-full ${
                statusColors[model.status as keyof typeof statusColors]
              }`}
            >
              {statusLabels[model.status as keyof typeof statusLabels]}
            </span>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">模型类型</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {typeLabels[model.type as keyof typeof typeLabels]}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">版本</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{model.version}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">准确率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(metrics.accuracy * 100)}%</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Model Info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <span className="text-sm font-medium text-gray-700">模型名称</span>
                <div className="mt-1 text-sm text-gray-900">{model.name}</div>
              </div>

              <div>
                <span className="text-sm font-medium text-gray-700">描述</span>
                <div className="mt-1 text-sm text-gray-900">{model.description}</div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-500">
                  <Cpu className="h-4 w-4 mr-2" />
                  模型类型：{typeLabels[model.type as keyof typeof typeLabels]}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Activity className="h-4 w-4 mr-2" />
                  关联项目：{project?.name || '未关联项目'}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  创建时间：{model.createdAt.toLocaleDateString()}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  更新时间：{model.updatedAt.toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>技术规格</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">框架</span>
                <span className="text-sm font-medium">TensorFlow</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">架构</span>
                <span className="text-sm font-medium">ResNet-50</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">参数量</span>
                <span className="text-sm font-medium">25.6M</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">模型大小</span>
                <span className="text-sm font-medium">98.2 MB</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>性能指标</CardTitle>
              <CardDescription>
                模型在验证集上的表现
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>准确率 (Accuracy)</span>
                    <span>{Math.round(metrics.accuracy * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${metrics.accuracy * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>精确率 (Precision)</span>
                    <span>{Math.round(metrics.precision * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${metrics.precision * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>召回率 (Recall)</span>
                    <span>{Math.round(metrics.recall * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{ width: `${metrics.recall * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>F1分数 (F1-Score)</span>
                    <span>{Math.round(metrics.f1Score * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-orange-600 h-2 rounded-full"
                      style={{ width: `${metrics.f1Score * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-3 bg-gray-50 rounded-md">
                <h4 className="text-sm font-medium text-gray-700 mb-2">训练配置：</h4>
                <div className="space-y-1 text-xs text-gray-600">
                  <div className="flex justify-between">
                    <span>学习率</span>
                    <span>0.001</span>
                  </div>
                  <div className="flex justify-between">
                    <span>批次大小</span>
                    <span>32</span>
                  </div>
                  <div className="flex justify-between">
                    <span>训练轮数</span>
                    <span>100</span>
                  </div>
                  <div className="flex justify-between">
                    <span>优化器</span>
                    <span>Adam</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
