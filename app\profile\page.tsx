'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { User, Mail, Calendar, Award, Activity, Settings } from 'lucide-react'
import { mockUsers, getProjectsByUserId, getTasksByUserId, getAnnotationsByUserId } from '@/services/mockData'

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false)
  const [userInfo, setUserInfo] = useState({
    username: '',
    email: '',
    fullName: '',
    role: '',
    joinDate: '',
    avatar: '',
  })
  const [stats, setStats] = useState({
    projectsCreated: 0,
    tasksCompleted: 0,
    annotationsApproved: 0,
    modelsDeployed: 0,
  })

  useEffect(() => {
    // Get current user from localStorage
    const currentUsername = localStorage.getItem('currentUser') || 'admin'
    const user = mockUsers.find(u => u.username === currentUsername)

    if (user) {
      setUserInfo({
        username: user.username,
        email: user.email,
        fullName: user.username,
        role: user.role === 'ADMIN' ? '系统管理员' :
              user.role === 'MANAGER' ? '项目管理员' :
              user.role === 'ANNOTATOR' ? '标注员' : '查看者',
        joinDate: user.createdAt.toLocaleDateString(),
        avatar: user.avatar || '',
      })

      // Calculate user statistics
      const userProjects = getProjectsByUserId(user.id)
      const userTasks = getTasksByUserId(user.id)
      const userAnnotations = getAnnotationsByUserId(user.id)

      setStats({
        projectsCreated: userProjects.filter(p => p.createdBy === user.id).length,
        tasksCompleted: userTasks.filter(t => t.status === 'COMPLETED').length,
        annotationsApproved: userAnnotations.filter(a => a.status === 'APPROVED').length,
        modelsDeployed: Math.floor(Math.random() * 5) + 1, // Random for demo
      })
    }
  }, [])

  const handleSave = () => {
    setIsEditing(false)
    // Save user info logic here
  }

  const handleInputChange = (field: string, value: string) => {
    setUserInfo(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">个人中心</h1>
          <p className="text-gray-600 mt-2">管理您的个人信息和设置</p>
        </div>
        <Button
          onClick={() => setIsEditing(!isEditing)}
          variant={isEditing ? "outline" : "default"}
        >
          <Settings className="h-4 w-4 mr-2" />
          {isEditing ? '取消编辑' : '编辑资料'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Info */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>个人信息</CardTitle>
              <CardDescription>
                您的基本信息和账户设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Avatar */}
              <div className="flex items-center space-x-4">
                <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                  <User className="h-10 w-10 text-gray-400" />
                </div>
                {isEditing && (
                  <Button variant="outline" size="sm">
                    更换头像
                  </Button>
                )}
              </div>

              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    用户名
                  </label>
                  <Input
                    value={userInfo.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    邮箱
                  </label>
                  <Input
                    value={userInfo.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    姓名
                  </label>
                  <Input
                    value={userInfo.fullName}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    角色
                  </label>
                  <Input
                    value={userInfo.role}
                    disabled={true}
                  />
                </div>
              </div>

              <div className="flex items-center text-sm text-gray-500">
                <Calendar className="h-4 w-4 mr-2" />
                加入时间：{userInfo.joinDate}
              </div>

              {isEditing && (
                <div className="flex space-x-4">
                  <Button onClick={handleSave}>
                    保存更改
                  </Button>
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    取消
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Stats */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>统计信息</CardTitle>
              <CardDescription>
                您的活动统计
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="h-4 w-4 mr-2 text-blue-500" />
                  <span className="text-sm">创建项目</span>
                </div>
                <span className="font-semibold">{stats.projectsCreated}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Award className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">完成任务</span>
                </div>
                <span className="font-semibold">{stats.tasksCompleted}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-purple-500" />
                  <span className="text-sm">审批标注</span>
                </div>
                <span className="font-semibold">{stats.annotationsApproved}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Settings className="h-4 w-4 mr-2 text-orange-500" />
                  <span className="text-sm">部署模型</span>
                </div>
                <span className="font-semibold">{stats.modelsDeployed}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最近活动</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-sm">
                  <p className="font-medium">创建了新项目</p>
                  <p className="text-gray-500">2小时前</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">审批了标注结果</p>
                  <p className="text-gray-500">5小时前</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">部署了新模型</p>
                  <p className="text-gray-500">1天前</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
