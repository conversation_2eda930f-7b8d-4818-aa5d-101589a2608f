"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-selector.tsx":
/*!**************************************************!*\
  !*** ./components/annotation/image-selector.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageSelector: () => (/* binding */ ImageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ ImageSelector auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ImageSelector(param) {\n    let { onImageSelect, selectedImageId, datasetId } = param;\n    _s();\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFormat, setSelectedFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageSelector.useEffect\": ()=>{\n            loadImages();\n        }\n    }[\"ImageSelector.useEffect\"], [\n        datasetId,\n        selectedFormat,\n        selectedTags\n    ]);\n    const loadImages = async ()=>{\n        setLoading(true);\n        try {\n            const params = {\n                datasetId,\n                format: selectedFormat === 'all' ? undefined : selectedFormat,\n                tags: selectedTags.length > 0 ? selectedTags : undefined\n            };\n            const imageList = await _services_imageService__WEBPACK_IMPORTED_MODULE_7__.imageService.getImages(params);\n            setImages(imageList);\n        } catch (error) {\n            console.error('Failed to load images:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Filter images by search term\n    const filteredImages = images.filter((image)=>{\n        var _image_tags;\n        return image.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_image_tags = image.tags) === null || _image_tags === void 0 ? void 0 : _image_tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    });\n    // Get unique formats and tags\n    const availableFormats = [\n        ...new Set(images.map((img)=>img.format))\n    ];\n    const availableTags = [\n        ...new Set(images.flatMap((img)=>img.tags || []))\n    ];\n    const handleTagToggle = (tag)=>{\n        setSelectedTags((prev)=>prev.includes(tag) ? prev.filter((t)=>t !== tag) : [\n                ...prev,\n                tag\n            ]);\n    };\n    const handleRandomSelect = async ()=>{\n        try {\n            const randomImage = await _services_imageService__WEBPACK_IMPORTED_MODULE_7__.imageService.getRandomImage(datasetId);\n            if (randomImage) {\n                onImageSelect(randomImage);\n            }\n        } catch (error) {\n            console.error('Failed to get random image:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"选择图像\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: \"正在加载图像列表...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 animate-spin text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            \"选择图像\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: [\n                            \"选择要进行标注的图像 (\",\n                            filteredImages.length,\n                            \" 张可用)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"搜索图像名称或标签...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        value: selectedFormat,\n                                        onValueChange: setSelectedFormat,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: \"格式\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"所有格式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    availableFormats.map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: format,\n                                                            children: format.toUpperCase()\n                                                        }, format, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleRandomSelect,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"随机选择\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            availableTags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"标签筛选:\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: availableTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-2 py-1 text-xs rounded-full border transition-colors cursor-pointer \".concat(selectedTags.includes(tag) ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'),\n                                                onClick: ()=>handleTagToggle(tag),\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3 max-h-96 overflow-y-auto\",\n                        children: filteredImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative cursor-pointer rounded-lg border-2 transition-all \".concat(selectedImageId === image.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'),\n                                onClick: ()=>onImageSelect(image),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: image.url,\n                                            alt: image.name,\n                                            className: \"w-full h-full object-cover\",\n                                            loading: \"lazy\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium truncate\",\n                                                children: image.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    image.width,\n                                                    \"\\xd7\",\n                                                    image.height,\n                                                    \" • \",\n                                                    (image.size / 1024).toFixed(1),\n                                                    \"KB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            image.tags && image.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                children: [\n                                                    image.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"text-xs px-1 py-0\",\n                                                            children: tag\n                                                        }, tag, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this)),\n                                                    image.tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs px-1 py-0\",\n                                                        children: [\n                                                            \"+\",\n                                                            image.tags.length - 2\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, image.id, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    filteredImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-2 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"没有找到匹配的图像\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"尝试调整搜索条件或筛选器\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageSelector, \"cT+WXUdh5uud5stShw9g4Jm8hrg=\");\n_c = ImageSelector;\nvar _c;\n$RefreshReg$(_c, \"ImageSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-selector.tsx\n"));

/***/ })

});