'use client'

import { usePathname } from 'next/navigation'
import { Navbar } from './navbar'

interface LayoutWrapperProps {
  children: React.ReactNode
}

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname()
  
  // Pages that should not show the navbar
  const noNavbarPages = ['/login']
  
  const shouldShowNavbar = !noNavbarPages.includes(pathname)

  if (!shouldShowNavbar) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  )
}
