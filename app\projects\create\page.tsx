'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { ArrowLeft } from 'lucide-react'
import { mockUsers, mockProjects } from '@/services/mockData'

export default function CreateProjectPage() {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'classification',
  })
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate project creation with mock data
    setTimeout(() => {
      const currentUser = localStorage.getItem('currentUser') || 'admin'
      const user = mockUsers.find(u => u.username === currentUser)

      const newProject = {
        id: (mockProjects.length + 1).toString(),
        name: formData.name,
        description: formData.description,
        status: 'ACTIVE' as any,
        createdBy: user?.id || '1',
        members: [
          { userId: user?.id || '1', role: 'OWNER' as any, joinedAt: new Date() }
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      // In a real app, this would be sent to the backend
      console.log('Creating project:', newProject)

      setIsLoading(false)
      alert(`项目 "${formData.name}" 创建成功！`)
      router.push(ROUTES.PROJECTS)
    }, 1000)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">创建项目</h1>
          <p className="text-gray-600 mt-2">创建一个新的数据标注项目</p>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>项目信息</CardTitle>
            <CardDescription>
              请填写项目的基本信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  项目名称 *
                </label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="请输入项目名称"
                  required
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  项目描述
                </label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="请输入项目描述"
                  rows={4}
                  className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                  项目类型 *
                </label>
                <select
                  id="type"
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  required
                >
                  <option value="classification">图像分类</option>
                  <option value="detection">目标检测</option>
                  <option value="segmentation">图像分割</option>
                  <option value="nlp">自然语言处理</option>
                  <option value="audio">音频处理</option>
                  <option value="video">视频处理</option>
                </select>
              </div>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || !formData.name}
                  className="flex-1"
                >
                  {isLoading ? '创建中...' : '创建项目'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
