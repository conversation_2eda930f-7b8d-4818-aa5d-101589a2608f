'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, Image as ImageIcon, Filter, RefreshCw } from 'lucide-react'
import { ImageData } from '@/services/mockData'
import { imageService } from '@/services/imageService'

interface ImageSelectorProps {
  onImageSelect: (image: ImageData) => void
  selectedImageId?: string
  datasetId?: string
}

export function ImageSelector({ onImageSelect, selectedImageId, datasetId }: ImageSelectorProps) {
  const [images, setImages] = useState<ImageData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFormat, setSelectedFormat] = useState<string>('all')
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  // Load images
  useEffect(() => {
    loadImages()
  }, [datasetId, selectedFormat, selectedTags])

  const loadImages = async () => {
    setLoading(true)
    try {
      const params = {
        datasetId,
        format: selectedFormat === 'all' ? undefined : selectedFormat,
        tags: selectedTags.length > 0 ? selectedTags : undefined
      }
      console.log('Loading images with params:', params)
      const imageList = await imageService.getImages(params)
      console.log('Loaded images:', imageList)
      setImages(imageList)
    } catch (error) {
      console.error('Failed to load images:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter images by search term
  const filteredImages = images.filter(image =>
    image.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    image.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  // Get unique formats and tags
  const availableFormats = [...new Set(images.map(img => img.format))]
  const availableTags = [...new Set(images.flatMap(img => img.tags || []))]

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  const handleRandomSelect = async () => {
    try {
      const randomImage = await imageService.getRandomImage(datasetId)
      if (randomImage) {
        onImageSelect(randomImage)
      }
    } catch (error) {
      console.error('Failed to get random image:', error)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>选择图像</CardTitle>
          <CardDescription>正在加载图像列表...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <ImageIcon className="h-5 w-5 mr-2" />
          选择图像
        </CardTitle>
        <CardDescription>
          选择要进行标注的图像 ({filteredImages.length} 张可用)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索图像名称或标签..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={selectedFormat} onValueChange={setSelectedFormat}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="格式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有格式</SelectItem>
                {availableFormats.map(format => (
                  <SelectItem key={format} value={format}>
                    {format.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={handleRandomSelect}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              随机选择
            </Button>
          </div>

          {/* Tags Filter */}
          {availableTags.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700">标签筛选:</div>
              <div className="flex flex-wrap gap-1">
                {availableTags.map(tag => (
                  <button
                    key={tag}
                    className={`px-2 py-1 text-xs rounded-full border transition-colors cursor-pointer ${
                      selectedTags.includes(tag)
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Image Grid */}
        <div className="grid grid-cols-2 gap-3 max-h-96 overflow-y-auto">
          {filteredImages.map((image) => (
            <div
              key={image.id}
              className={`relative cursor-pointer rounded-lg border-2 transition-all ${
                selectedImageId === image.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onImageSelect(image)}
            >
              <div className="aspect-video relative overflow-hidden rounded-t-lg">
                <img
                  src={image.url}
                  alt={image.name}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
              <div className="p-2">
                <div className="text-xs font-medium truncate">{image.name}</div>
                <div className="text-xs text-gray-500">
                  {image.width}×{image.height} • {(image.size / 1024).toFixed(1)}KB
                </div>
                {image.tags && image.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {image.tags.slice(0, 2).map(tag => (
                      <span key={tag} className="px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                    {image.tags.length > 2 && (
                      <span className="px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded">
                        +{image.tags.length - 2}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredImages.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <ImageIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
            <p>没有找到匹配的图像</p>
            <p className="text-sm">尝试调整搜索条件或筛选器</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
