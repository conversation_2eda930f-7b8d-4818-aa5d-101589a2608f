"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./app/annotations/image-annotate/page.tsx":
/*!*************************************************!*\
  !*** ./app/annotations/image-annotate/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageAnnotatePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/routes */ \"(app-pages-browser)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/annotation/image-annotation-canvas */ \"(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\");\n/* harmony import */ var _components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/annotation/annotation-toolbar */ \"(app-pages-browser)/./components/annotation/annotation-toolbar.tsx\");\n/* harmony import */ var _components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/annotation/annotation-list */ \"(app-pages-browser)/./components/annotation/annotation-list.tsx\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ImageAnnotatePage() {\n    _s();\n    const [currentTool, setCurrentTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [annotations, setAnnotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnnotation, setSelectedAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentLabel, setCurrentLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showLabels, setShowLabels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Load image from URL params or show selector\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotatePage.useEffect\": ()=>{\n            const imageId = searchParams.get('imageId');\n            const datasetId = searchParams.get('datasetId');\n            if (imageId) {\n                loadImageById(imageId);\n            } else if (datasetId) {\n                loadRandomImageFromDataset(datasetId);\n            } else {\n                // Load a default image for immediate use\n                loadDefaultImage();\n            }\n        }\n    }[\"ImageAnnotatePage.useEffect\"], [\n        searchParams\n    ]);\n    const loadDefaultImage = async ()=>{\n        setLoading(true);\n        try {\n            // Load the first available image as default\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_9__.imageService.getRandomImage();\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            } else {\n                // If no images available, show selector\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load default image:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadImageById = async (imageId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_9__.imageService.getImage(imageId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRandomImageFromDataset = async (datasetId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_9__.imageService.getRandomImage(datasetId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load random image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Predefined labels with colors\n    const predefinedLabels = [\n        {\n            name: '人物',\n            color: '#ef4444'\n        },\n        {\n            name: '车辆',\n            color: '#3b82f6'\n        },\n        {\n            name: '建筑',\n            color: '#10b981'\n        },\n        {\n            name: '动物',\n            color: '#f59e0b'\n        },\n        {\n            name: '植物',\n            color: '#8b5cf6'\n        },\n        {\n            name: '物体',\n            color: '#ec4899'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    // Event handlers\n    const handleAnnotationCreate = (annotation)=>{\n        setAnnotations((prev)=>[\n                ...prev,\n                annotation\n            ]);\n        saveToHistory([\n            ...annotations,\n            annotation\n        ]);\n    };\n    const handleAnnotationUpdate = (id, updates)=>{\n        setAnnotations((prev)=>prev.map((ann)=>ann.id === id ? {\n                    ...ann,\n                    ...updates\n                } : ann));\n    };\n    const handleAnnotationDelete = (id)=>{\n        setAnnotations((prev)=>prev.filter((ann)=>ann.id !== id));\n        setSelectedAnnotation(null);\n        saveToHistory(annotations.filter((ann)=>ann.id !== id));\n    };\n    const handleAnnotationVisibilityToggle = (id)=>{\n        // For now, just select/deselect the annotation\n        setSelectedAnnotation(selectedAnnotation === id ? null : id);\n    };\n    const saveToHistory = (newAnnotations)=>{\n        const annotationsToSave = newAnnotations || annotations;\n        const newHistory = history.slice(0, historyIndex + 1);\n        newHistory.push([\n            ...annotationsToSave\n        ]);\n        setHistory(newHistory);\n        setHistoryIndex(newHistory.length - 1);\n    };\n    const handleUndo = ()=>{\n        if (historyIndex > 0) {\n            setHistoryIndex(historyIndex - 1);\n            setAnnotations([\n                ...history[historyIndex - 1]\n            ]);\n        }\n    };\n    const handleRedo = ()=>{\n        if (historyIndex < history.length - 1) {\n            setHistoryIndex(historyIndex + 1);\n            setAnnotations([\n                ...history[historyIndex + 1]\n            ]);\n        }\n    };\n    const handleClear = ()=>{\n        if (window.confirm('确定要清空所有标注吗？')) {\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n            saveToHistory([]);\n        }\n    };\n    const handleSave = ()=>{\n        // Save annotations logic here\n        console.log('Saving annotations:', annotations);\n        alert(\"已保存 \".concat(annotations.length, \" 个标注\"));\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_5__.ROUTES.ANNOTATIONS);\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载图像...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Show image selector if no image is selected\n    if (showImageSelector || !selectedImage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"选择要标注的图像\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"选择图像进行标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"点击下方图像开始标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        {\n                                            id: 'img_001',\n                                            name: '猫咪图片',\n                                            url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_002',\n                                            name: '街景图片',\n                                            url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_003',\n                                            name: '狗狗图片',\n                                            url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_004',\n                                            name: '建筑图片',\n                                            url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_005',\n                                            name: '风景图片',\n                                            url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'\n                                        }\n                                    ].map((img)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"cursor-pointer rounded-lg border-2 border-gray-200 hover:border-blue-500 transition-colors\",\n                                            onClick: ()=>{\n                                                setSelectedImage({\n                                                    id: img.id,\n                                                    name: img.name,\n                                                    url: img.url,\n                                                    width: 800,\n                                                    height: 600,\n                                                    size: 200000,\n                                                    format: 'jpg',\n                                                    datasetId: '1',\n                                                    uploadedAt: new Date(),\n                                                    tags: []\n                                                });\n                                                setShowImageSelector(false);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: img.url,\n                                                        alt: img.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: img.name\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, img.id, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: selectedImage.name\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>setShowImageSelector(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                \"切换图像\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_7__.AnnotationToolbar, {\n                                currentTool: currentTool,\n                                onToolChange: setCurrentTool,\n                                currentLabel: currentLabel,\n                                onLabelChange: setCurrentLabel,\n                                predefinedLabels: predefinedLabels,\n                                zoom: zoom,\n                                onZoomChange: setZoom,\n                                showLabels: showLabels,\n                                onShowLabelsToggle: ()=>setShowLabels(!showLabels),\n                                canUndo: historyIndex > 0,\n                                canRedo: historyIndex < history.length - 1,\n                                onUndo: handleUndo,\n                                onRedo: handleRedo,\n                                onSave: handleSave,\n                                onClear: handleClear\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_8__.AnnotationList, {\n                                annotations: annotations,\n                                selectedAnnotation: selectedAnnotation,\n                                onAnnotationSelect: setSelectedAnnotation,\n                                onAnnotationDelete: handleAnnotationDelete,\n                                onAnnotationUpdate: handleAnnotationUpdate,\n                                onAnnotationVisibilityToggle: handleAnnotationVisibilityToggle\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"图像标注区域\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: [\n                                                \"使用左侧工具在图像上创建标注 - 当前工具: \",\n                                                currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-auto border rounded-lg bg-gray-50\",\n                                            style: {\n                                                maxHeight: '600px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"scale(\".concat(zoom, \")\"),\n                                                    transformOrigin: 'top left'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_6__.ImageAnnotationCanvas, {\n                                                    imageUrl: selectedImage.url,\n                                                    imageWidth: selectedImage.width,\n                                                    imageHeight: selectedImage.height,\n                                                    annotations: annotations,\n                                                    selectedAnnotation: selectedAnnotation,\n                                                    currentTool: currentTool,\n                                                    currentLabel: currentLabel,\n                                                    currentColor: getCurrentColor(),\n                                                    zoom: zoom,\n                                                    showLabels: showLabels,\n                                                    onAnnotationCreate: handleAnnotationCreate,\n                                                    onAnnotationUpdate: handleAnnotationUpdate,\n                                                    onAnnotationSelect: setSelectedAnnotation\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"使用说明:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 选择工具和标签后，在图像上拖拽创建标注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 点击标注可以选中，在右侧列表中编辑标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 使用缩放工具可以放大查看细节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 支持撤销/重做操作，可以随时恢复之前的状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"总标注数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.filter((a)=>a.label).length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"已标记\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                Math.round(zoom * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"缩放比例\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: history.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"历史记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotatePage, \"9pHOkhHZxupwN/FxwGc1+4XtiAM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ImageAnnotatePage;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/annotations/image-annotate/page.tsx\n"));

/***/ })

});