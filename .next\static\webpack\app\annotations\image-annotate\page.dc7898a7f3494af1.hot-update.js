"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./app/annotations/image-annotate/page.tsx":
/*!*************************************************!*\
  !*** ./app/annotations/image-annotate/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageAnnotatePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/routes */ \"(app-pages-browser)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/annotation/image-annotation-canvas */ \"(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\");\n/* harmony import */ var _components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/annotation/annotation-toolbar */ \"(app-pages-browser)/./components/annotation/annotation-toolbar.tsx\");\n/* harmony import */ var _components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/annotation/annotation-list */ \"(app-pages-browser)/./components/annotation/annotation-list.tsx\");\n/* harmony import */ var _components_annotation_image_selector__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../components/annotation/image-selector */ \"(app-pages-browser)/./components/annotation/image-selector.tsx\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ImageAnnotatePage() {\n    _s();\n    const [currentTool, setCurrentTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [annotations, setAnnotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnnotation, setSelectedAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentLabel, setCurrentLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showLabels, setShowLabels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Load image from URL params or show selector\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotatePage.useEffect\": ()=>{\n            const imageId = searchParams.get('imageId');\n            const datasetId = searchParams.get('datasetId');\n            if (imageId) {\n                loadImageById(imageId);\n            } else if (datasetId) {\n                loadRandomImageFromDataset(datasetId);\n            } else {\n                // Load a default image for immediate use\n                loadDefaultImage();\n            }\n        }\n    }[\"ImageAnnotatePage.useEffect\"], [\n        searchParams\n    ]);\n    const loadDefaultImage = async ()=>{\n        setLoading(true);\n        try {\n            // Load the first available image as default\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_10__.imageService.getRandomImage();\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            } else {\n                // If no images available, show selector\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load default image:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadImageById = async (imageId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_10__.imageService.getImage(imageId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRandomImageFromDataset = async (datasetId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_10__.imageService.getRandomImage(datasetId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load random image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleImageSelect = (image)=>{\n        setSelectedImage(image);\n        setShowImageSelector(false);\n        // Clear existing annotations when switching images\n        setAnnotations([]);\n        setSelectedAnnotation(null);\n        setHistory([]);\n        setHistoryIndex(-1);\n    };\n    // Predefined labels with colors\n    const predefinedLabels = [\n        {\n            name: '人物',\n            color: '#ef4444'\n        },\n        {\n            name: '车辆',\n            color: '#3b82f6'\n        },\n        {\n            name: '建筑',\n            color: '#10b981'\n        },\n        {\n            name: '动物',\n            color: '#f59e0b'\n        },\n        {\n            name: '植物',\n            color: '#8b5cf6'\n        },\n        {\n            name: '物体',\n            color: '#ec4899'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    // Event handlers\n    const handleAnnotationCreate = (annotation)=>{\n        setAnnotations((prev)=>[\n                ...prev,\n                annotation\n            ]);\n        saveToHistory([\n            ...annotations,\n            annotation\n        ]);\n    };\n    const handleAnnotationUpdate = (id, updates)=>{\n        setAnnotations((prev)=>prev.map((ann)=>ann.id === id ? {\n                    ...ann,\n                    ...updates\n                } : ann));\n    };\n    const handleAnnotationDelete = (id)=>{\n        setAnnotations((prev)=>prev.filter((ann)=>ann.id !== id));\n        setSelectedAnnotation(null);\n        saveToHistory(annotations.filter((ann)=>ann.id !== id));\n    };\n    const handleAnnotationVisibilityToggle = (id)=>{\n        // For now, just select/deselect the annotation\n        setSelectedAnnotation(selectedAnnotation === id ? null : id);\n    };\n    const saveToHistory = (newAnnotations)=>{\n        const annotationsToSave = newAnnotations || annotations;\n        const newHistory = history.slice(0, historyIndex + 1);\n        newHistory.push([\n            ...annotationsToSave\n        ]);\n        setHistory(newHistory);\n        setHistoryIndex(newHistory.length - 1);\n    };\n    const handleUndo = ()=>{\n        if (historyIndex > 0) {\n            setHistoryIndex(historyIndex - 1);\n            setAnnotations([\n                ...history[historyIndex - 1]\n            ]);\n        }\n    };\n    const handleRedo = ()=>{\n        if (historyIndex < history.length - 1) {\n            setHistoryIndex(historyIndex + 1);\n            setAnnotations([\n                ...history[historyIndex + 1]\n            ]);\n        }\n    };\n    const handleClear = ()=>{\n        if (window.confirm('确定要清空所有标注吗？')) {\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n            saveToHistory([]);\n        }\n    };\n    const handleSave = ()=>{\n        // Save annotations logic here\n        console.log('Saving annotations:', annotations);\n        alert(\"已保存 \".concat(annotations.length, \" 个标注\"));\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_5__.ROUTES.ANNOTATIONS);\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载图像...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    // Show image selector if no image is selected\n    if (showImageSelector || !selectedImage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"选择要标注的图像\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_selector__WEBPACK_IMPORTED_MODULE_9__.ImageSelector, {\n                        onImageSelect: handleImageSelect,\n                        datasetId: searchParams.get('datasetId') || undefined\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: selectedImage.name\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>setShowImageSelector(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Image_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                \"切换图像\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_7__.AnnotationToolbar, {\n                                currentTool: currentTool,\n                                onToolChange: setCurrentTool,\n                                currentLabel: currentLabel,\n                                onLabelChange: setCurrentLabel,\n                                predefinedLabels: predefinedLabels,\n                                zoom: zoom,\n                                onZoomChange: setZoom,\n                                showLabels: showLabels,\n                                onShowLabelsToggle: ()=>setShowLabels(!showLabels),\n                                canUndo: historyIndex > 0,\n                                canRedo: historyIndex < history.length - 1,\n                                onUndo: handleUndo,\n                                onRedo: handleRedo,\n                                onSave: handleSave,\n                                onClear: handleClear\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_8__.AnnotationList, {\n                                annotations: annotations,\n                                selectedAnnotation: selectedAnnotation,\n                                onAnnotationSelect: setSelectedAnnotation,\n                                onAnnotationDelete: handleAnnotationDelete,\n                                onAnnotationUpdate: handleAnnotationUpdate,\n                                onAnnotationVisibilityToggle: handleAnnotationVisibilityToggle\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"图像标注区域\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: [\n                                                \"使用左侧工具在图像上创建标注 - 当前工具: \",\n                                                currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-auto border rounded-lg bg-gray-50\",\n                                            style: {\n                                                maxHeight: '600px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"scale(\".concat(zoom, \")\"),\n                                                    transformOrigin: 'top left'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_6__.ImageAnnotationCanvas, {\n                                                    imageUrl: selectedImage.url,\n                                                    imageWidth: selectedImage.width,\n                                                    imageHeight: selectedImage.height,\n                                                    annotations: annotations,\n                                                    selectedAnnotation: selectedAnnotation,\n                                                    currentTool: currentTool,\n                                                    currentLabel: currentLabel,\n                                                    currentColor: getCurrentColor(),\n                                                    zoom: zoom,\n                                                    showLabels: showLabels,\n                                                    onAnnotationCreate: handleAnnotationCreate,\n                                                    onAnnotationUpdate: handleAnnotationUpdate,\n                                                    onAnnotationSelect: setSelectedAnnotation\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"使用说明:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 选择工具和标签后，在图像上拖拽创建标注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 点击标注可以选中，在右侧列表中编辑标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 使用缩放工具可以放大查看细节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 支持撤销/重做操作，可以随时恢复之前的状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"总标注数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.filter((a)=>a.label).length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"已标记\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                Math.round(zoom * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"缩放比例\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: history.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"历史记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotatePage, \"9pHOkhHZxupwN/FxwGc1+4XtiAM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ImageAnnotatePage;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/annotations/image-annotate/page.tsx\n"));

/***/ })

});