'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Image as ImageIcon } from 'lucide-react'
import { ImageData, getAllImages } from '@/services/mockData'

export default function ImageAnnotateTestPage() {
  const [images, setImages] = useState<ImageData[]>([])
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    loadImages()
  }, [])

  const loadImages = async () => {
    try {
      const imageList = getAllImages()
      console.log('Loaded images:', imageList)
      setImages(imageList)
      if (imageList.length > 0) {
        setSelectedImage(imageList[0])
      }
    } catch (error) {
      console.error('Failed to load images:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载图像...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">图像标注测试</h1>
            <p className="text-gray-600 mt-2">
              {selectedImage ? selectedImage.name : '没有选择图像'}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Image Selection */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ImageIcon className="h-5 w-5 mr-2" />
                图像列表 ({images.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {images.map((image) => (
                  <div
                    key={image.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedImage?.id === image.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedImage(image)}
                  >
                    <div className="text-sm font-medium">{image.name}</div>
                    <div className="text-xs text-gray-500">
                      {image.width}×{image.height} • {image.format.toUpperCase()}
                    </div>
                    {image.tags && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {image.tags.map(tag => (
                          <span key={tag} className="px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Image Display */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>图像预览</CardTitle>
              <CardDescription>
                {selectedImage ? `显示: ${selectedImage.name}` : '请选择一个图像'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedImage ? (
                <div className="space-y-4">
                  <div className="relative border rounded-lg overflow-hidden bg-gray-50">
                    <img
                      src={selectedImage.url}
                      alt={selectedImage.name}
                      className="w-full h-auto max-h-96 object-contain"
                      onLoad={() => console.log('Image loaded successfully')}
                      onError={(e) => console.error('Image failed to load:', e)}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">尺寸:</span> {selectedImage.width}×{selectedImage.height}
                    </div>
                    <div>
                      <span className="font-medium">格式:</span> {selectedImage.format.toUpperCase()}
                    </div>
                    <div>
                      <span className="font-medium">大小:</span> {(selectedImage.size / 1024).toFixed(1)}KB
                    </div>
                    <div>
                      <span className="font-medium">数据集:</span> {selectedImage.datasetId}
                    </div>
                  </div>

                  {selectedImage.tags && selectedImage.tags.length > 0 && (
                    <div>
                      <div className="font-medium text-sm mb-2">标签:</div>
                      <div className="flex flex-wrap gap-1">
                        {selectedImage.tags.map(tag => (
                          <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t">
                    <Button 
                      onClick={() => {
                        const url = `/annotations/image-annotate?imageId=${selectedImage.id}`
                        router.push(url)
                      }}
                      className="w-full"
                    >
                      开始标注此图像
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <ImageIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>请从左侧选择一个图像</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">总图像数:</span> {images.length}</div>
            <div><span className="font-medium">当前选择:</span> {selectedImage?.id || '无'}</div>
            <div><span className="font-medium">图像URL:</span> {selectedImage?.url || '无'}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
