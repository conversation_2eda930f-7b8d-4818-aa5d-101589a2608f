'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ROUTES } from '@/constants/routes'
import { ArrowLeft, Edit, Tag, CheckCircle, User, Calendar } from 'lucide-react'
import { mockAnnotations, getTaskById, getUserById } from '@/services/mockData'

interface AnnotationDetailProps {
  params: Promise<{
    id: string
  }>
}

export default function AnnotationDetailPage({ params }: AnnotationDetailProps) {
  const [annotation, setAnnotation] = useState<any>(null)
  const [paramId, setParamId] = useState<string>('')
  const router = useRouter()

  useEffect(() => {
    // Await params and load annotation data
    params.then(({ id }) => {
      setParamId(id)
      const annotationData = mockAnnotations.find(a => a.id === id)
      setAnnotation(annotationData)
    })
  }, [params])

  if (!annotation) {
    return <div className="flex items-center justify-center min-h-screen">加载中...</div>
  }

  const task = getTaskById(annotation.taskId)
  const annotator = getUserById(annotation.annotatorId)

  // Extract labels from annotation data
  const getLabels = () => {
    if (annotation.data.category) return [annotation.data.category]
    if (annotation.data.objects) return annotation.data.objects.map((obj: any) => obj.class)
    if (annotation.data.sentiment) return [annotation.data.sentiment]
    if (annotation.data.transcription) return ['转录文本']
    return ['未知']
  }

  // Get confidence from annotation data
  const getConfidence = () => {
    if (annotation.data.confidence) return annotation.data.confidence
    if (annotation.data.objects) {
      const confidences = annotation.data.objects.map((obj: any) => obj.confidence)
      return confidences.reduce((a: number, b: number) => a + b, 0) / confidences.length
    }
    return 0.8 // default
  }

  const statusColors = {
    DRAFT: 'bg-gray-100 text-gray-800',
    SUBMITTED: 'bg-blue-100 text-blue-800',
    APPROVED: 'bg-green-100 text-green-800',
    REJECTED: 'bg-red-100 text-red-800',
  }

  const statusLabels = {
    DRAFT: '草稿',
    SUBMITTED: '已提交',
    APPROVED: '已批准',
    REJECTED: '已拒绝',
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">标注详情</h1>
            <p className="text-gray-600 mt-2">{annotation.dataItemId}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Annotation Info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">状态</span>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    statusColors[annotation.status as keyof typeof statusColors]
                  }`}
                >
                  {statusLabels[annotation.status as keyof typeof statusLabels]}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">置信度</span>
                <div className="flex items-center text-sm text-gray-500">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  {Math.round(getConfidence() * 100)}%
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-500">
                  <User className="h-4 w-4 mr-2" />
                  标注员：{annotator?.username}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  创建时间：{annotation.createdAt.toLocaleDateString()}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  更新时间：{annotation.updatedAt.toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>
                <Tag className="inline h-5 w-5 mr-2" />
                标签信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {getLabels().map((label, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-50 text-blue-700 text-sm rounded-md"
                  >
                    {label}
                  </span>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>关联信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-700">关联任务：</span>
                <span className="text-sm text-gray-900 ml-2">{task?.name}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">数据项：</span>
                <span className="text-sm text-gray-900 ml-2">{annotation.dataItemId}</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Preview */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>数据预览</CardTitle>
              <CardDescription>
                标注的数据项内容
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <div className="text-lg font-medium">{annotation.dataItemId}</div>
                  <p className="text-sm mt-2">数据预览区域</p>
                  <p className="text-xs mt-1">
                    {task?.type === 'CLASSIFICATION' && '图像分类数据'}
                    {task?.type === 'DETECTION' && '目标检测数据'}
                    {task?.type === 'TRANSCRIPTION' && '音频转录数据'}
                  </p>
                </div>
              </div>

              {/* Annotation Data Details */}
              <div className="mt-4 space-y-3">
                <h4 className="text-sm font-medium text-gray-700">标注数据：</h4>
                <div className="bg-gray-50 p-3 rounded-md">
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                    {JSON.stringify(annotation.data, null, 2)}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
