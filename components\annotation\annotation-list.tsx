'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Trash2, Edit, Eye, EyeOff } from 'lucide-react'
import { AnnotationShape } from './image-annotation-canvas'

interface AnnotationListProps {
  annotations: AnnotationShape[]
  selectedAnnotation: string | null
  onAnnotationSelect: (id: string | null) => void
  onAnnotationDelete: (id: string) => void
  onAnnotationUpdate: (id: string, updates: Partial<AnnotationShape>) => void
  onAnnotationVisibilityToggle: (id: string) => void
}

export function AnnotationList({
  annotations,
  selectedAnnotation,
  onAnnotationSelect,
  onAnnotationDelete,
  onAnnotationUpdate,
  onAnnotationVisibilityToggle,
}: AnnotationListProps) {
  const getAnnotationSummary = (annotation: AnnotationShape) => {
    switch (annotation.type) {
      case 'rectangle':
        const [x, y, width, height] = annotation.coordinates
        return `矩形 (${Math.round(x)}, ${Math.round(y)}) ${Math.round(Math.abs(width))}×${Math.round(Math.abs(height))}`
      case 'circle':
        const [cx, cy, radius] = annotation.coordinates
        return `圆形 (${Math.round(cx)}, ${Math.round(cy)}) r=${Math.round(radius)}`
      case 'point':
        const [px, py] = annotation.coordinates
        return `点 (${Math.round(px)}, ${Math.round(py)})`
      case 'polygon':
        const pointCount = annotation.coordinates.length / 2
        return `多边形 (${pointCount}个点)`
      default:
        return '未知类型'
    }
  }

  const handleLabelEdit = (id: string, newLabel: string) => {
    onAnnotationUpdate(id, { label: newLabel })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">标注列表</CardTitle>
        <p className="text-sm text-gray-600">
          共 {annotations.length} 个标注
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {annotations.map((annotation, index) => (
            <div
              key={annotation.id}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedAnnotation === annotation.id 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onAnnotationSelect(
                selectedAnnotation === annotation.id ? null : annotation.id
              )}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <div
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: annotation.color }}
                    />
                    <span className="text-sm font-medium truncate">
                      {annotation.label || `标注 ${index + 1}`}
                    </span>
                  </div>
                  
                  <div className="text-xs text-gray-500 mb-2">
                    {getAnnotationSummary(annotation)}
                  </div>

                  {/* Editable Label */}
                  <Input
                    value={annotation.label}
                    onChange={(e) => handleLabelEdit(annotation.id, e.target.value)}
                    placeholder="输入标签名称"
                    className="text-xs h-7"
                    onClick={(e) => e.stopPropagation()}
                  />

                  {/* Confidence Score */}
                  {annotation.confidence && (
                    <div className="text-xs text-gray-500 mt-1">
                      置信度: {Math.round(annotation.confidence * 100)}%
                    </div>
                  )}
                </div>

                <div className="flex flex-col space-y-1 ml-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onAnnotationVisibilityToggle(annotation.id)
                    }}
                    className="h-6 w-6 p-0"
                  >
                    <Eye className="h-3 w-3" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onAnnotationDelete(annotation.id)
                    }}
                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {/* Coordinates Details (when selected) */}
              {selectedAnnotation === annotation.id && (
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <div className="text-xs text-gray-600">
                    <div className="font-medium mb-1">坐标详情:</div>
                    <div className="font-mono">
                      {annotation.coordinates.map((coord, i) => (
                        <span key={i}>
                          {Math.round(coord * 100) / 100}
                          {i < annotation.coordinates.length - 1 ? ', ' : ''}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}

          {annotations.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p className="text-sm">暂无标注</p>
              <p className="text-xs mt-1">选择工具开始标注</p>
            </div>
          )}
        </div>

        {/* Statistics */}
        {annotations.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="text-xs text-gray-600">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="font-medium">矩形:</span> {annotations.filter(a => a.type === 'rectangle').length}
                </div>
                <div>
                  <span className="font-medium">圆形:</span> {annotations.filter(a => a.type === 'circle').length}
                </div>
                <div>
                  <span className="font-medium">点:</span> {annotations.filter(a => a.type === 'point').length}
                </div>
                <div>
                  <span className="font-medium">多边形:</span> {annotations.filter(a => a.type === 'polygon').length}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
