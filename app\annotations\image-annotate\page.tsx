'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ROUTES } from '@/constants/routes'
import { ArrowLeft, Image as ImageIcon } from 'lucide-react'
import { ImageAnnotationCanvas, AnnotationShape } from '../../../components/annotation/image-annotation-canvas'
import { AnnotationToolbar, AnnotationTool } from '../../../components/annotation/annotation-toolbar'
import { AnnotationList } from '../../../components/annotation/annotation-list'
import { ImageData } from '@/services/mockData'
import { imageService } from '@/services/imageService'

export default function ImageAnnotatePage() {
  const [currentTool, setCurrentTool] = useState<AnnotationTool>('select')
  const [annotations, setAnnotations] = useState<AnnotationShape[]>([])
  const [selectedAnnotation, setSelectedAnnotation] = useState<string | null>(null)
  const [currentLabel, setCurrentLabel] = useState('')
  const [zoom, setZoom] = useState(1)
  const [showLabels, setShowLabels] = useState(true)
  const [history, setHistory] = useState<AnnotationShape[][]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null)
  const [showImageSelector, setShowImageSelector] = useState(true)
  const [loading, setLoading] = useState(false)

  const router = useRouter()
  const searchParams = useSearchParams()

  // Load image from URL params or show selector
  useEffect(() => {
    const imageId = searchParams.get('imageId')
    const datasetId = searchParams.get('datasetId')

    if (imageId) {
      loadImageById(imageId)
    } else if (datasetId) {
      loadRandomImageFromDataset(datasetId)
    } else {
      // Load a default image for immediate use
      loadDefaultImage()
    }
  }, [searchParams])

  const loadDefaultImage = async () => {
    setLoading(true)
    try {
      // Load the first available image as default
      const image = await imageService.getRandomImage()
      if (image) {
        setSelectedImage(image)
        setShowImageSelector(false)
      } else {
        // If no images available, show selector
        setShowImageSelector(true)
      }
    } catch (error) {
      console.error('Failed to load default image:', error)
      setShowImageSelector(true)
    } finally {
      setLoading(false)
    }
  }

  const loadImageById = async (imageId: string) => {
    setLoading(true)
    try {
      const image = await imageService.getImage(imageId)
      if (image) {
        setSelectedImage(image)
        setShowImageSelector(false)
      }
    } catch (error) {
      console.error('Failed to load image:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadRandomImageFromDataset = async (datasetId: string) => {
    setLoading(true)
    try {
      const image = await imageService.getRandomImage(datasetId)
      if (image) {
        setSelectedImage(image)
        setShowImageSelector(false)
      }
    } catch (error) {
      console.error('Failed to load random image:', error)
    } finally {
      setLoading(false)
    }
  }



  // Predefined labels with colors
  const predefinedLabels = [
    { name: '人物', color: '#ef4444' },
    { name: '车辆', color: '#3b82f6' },
    { name: '建筑', color: '#10b981' },
    { name: '动物', color: '#f59e0b' },
    { name: '植物', color: '#8b5cf6' },
    { name: '物体', color: '#ec4899' },
  ]

  const getCurrentColor = () => {
    const labelConfig = predefinedLabels.find(l => l.name === currentLabel)
    return labelConfig?.color || '#ef4444'
  }

  // Event handlers
  const handleAnnotationCreate = (annotation: AnnotationShape) => {
    setAnnotations(prev => [...prev, annotation])
    saveToHistory([...annotations, annotation])
  }

  const handleAnnotationUpdate = (id: string, updates: Partial<AnnotationShape>) => {
    setAnnotations(prev => prev.map(ann =>
      ann.id === id ? { ...ann, ...updates } : ann
    ))
  }

  const handleAnnotationDelete = (id: string) => {
    setAnnotations(prev => prev.filter(ann => ann.id !== id))
    setSelectedAnnotation(null)
    saveToHistory(annotations.filter(ann => ann.id !== id))
  }

  const handleAnnotationVisibilityToggle = (id: string) => {
    // For now, just select/deselect the annotation
    setSelectedAnnotation(selectedAnnotation === id ? null : id)
  }

  const saveToHistory = (newAnnotations?: AnnotationShape[]) => {
    const annotationsToSave = newAnnotations || annotations
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push([...annotationsToSave])
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }

  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setAnnotations([...history[historyIndex - 1]])
    }
  }

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setAnnotations([...history[historyIndex + 1]])
    }
  }

  const handleClear = () => {
    if (window.confirm('确定要清空所有标注吗？')) {
      setAnnotations([])
      setSelectedAnnotation(null)
      saveToHistory([])
    }
  }

  const handleSave = () => {
    // Save annotations logic here
    console.log('Saving annotations:', annotations)
    alert(`已保存 ${annotations.length} 个标注`)
    router.push(ROUTES.ANNOTATIONS)
  }

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载图像...</p>
        </div>
      </div>
    )
  }

  // Show image selector if no image is selected
  if (showImageSelector || !selectedImage) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              className="inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">图像标注</h1>
              <p className="text-gray-600 mt-2">选择要标注的图像</p>
            </div>
          </div>
        </div>

        {/* Simple Image Selector */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>选择图像进行标注</CardTitle>
              <CardDescription>点击下方图像开始标注</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {[
                  { id: 'img_001', name: '猫咪图片', url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop' },
                  { id: 'img_002', name: '街景图片', url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop' },
                  { id: 'img_003', name: '狗狗图片', url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop' },
                  { id: 'img_004', name: '建筑图片', url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop' },
                  { id: 'img_005', name: '风景图片', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop' }
                ].map((img) => (
                  <div
                    key={img.id}
                    className="cursor-pointer rounded-lg border-2 border-gray-200 hover:border-blue-500 transition-colors"
                    onClick={() => {
                      setSelectedImage({
                        id: img.id,
                        name: img.name,
                        url: img.url,
                        width: 800,
                        height: 600,
                        size: 200000,
                        format: 'jpg',
                        datasetId: '1',
                        uploadedAt: new Date(),
                        tags: []
                      })
                      setShowImageSelector(false)
                    }}
                  >
                    <div className="aspect-video relative overflow-hidden rounded-t-lg">
                      <img
                        src={img.url}
                        alt={img.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="p-3">
                      <div className="text-sm font-medium">{img.name}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            className="inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">图像标注</h1>
            <p className="text-gray-600 mt-2">{selectedImage.name}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className="inline-flex items-center justify-center h-9 rounded-md px-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground text-sm"
            onClick={() => setShowImageSelector(true)}
          >
            <ImageIcon className="h-4 w-4 mr-2" />
            切换图像
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar - Tools and Annotations */}
        <div className="space-y-4">
          <AnnotationToolbar
            currentTool={currentTool}
            onToolChange={setCurrentTool}
            currentLabel={currentLabel}
            onLabelChange={setCurrentLabel}
            predefinedLabels={predefinedLabels}
            zoom={zoom}
            onZoomChange={setZoom}
            showLabels={showLabels}
            onShowLabelsToggle={() => setShowLabels(!showLabels)}
            canUndo={historyIndex > 0}
            canRedo={historyIndex < history.length - 1}
            onUndo={handleUndo}
            onRedo={handleRedo}
            onSave={handleSave}
            onClear={handleClear}
          />

          <AnnotationList
            annotations={annotations}
            selectedAnnotation={selectedAnnotation}
            onAnnotationSelect={setSelectedAnnotation}
            onAnnotationDelete={handleAnnotationDelete}
            onAnnotationUpdate={handleAnnotationUpdate}
            onAnnotationVisibilityToggle={handleAnnotationVisibilityToggle}
          />
        </div>

        {/* Main Canvas Area */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>图像标注区域</CardTitle>
              <CardDescription>
                使用左侧工具在图像上创建标注 - 当前工具: {currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div
                className="relative overflow-auto border rounded-lg bg-gray-50"
                style={{ maxHeight: '600px' }}
              >
                <div style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}>
                  <ImageAnnotationCanvas
                    imageUrl={selectedImage.url}
                    imageWidth={selectedImage.width}
                    imageHeight={selectedImage.height}
                    annotations={annotations}
                    selectedAnnotation={selectedAnnotation}
                    currentTool={currentTool}
                    currentLabel={currentLabel}
                    currentColor={getCurrentColor()}
                    zoom={zoom}
                    showLabels={showLabels}
                    onAnnotationCreate={handleAnnotationCreate}
                    onAnnotationUpdate={handleAnnotationUpdate}
                    onAnnotationSelect={setSelectedAnnotation}
                  />
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明:</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• 选择工具和标签后，在图像上拖拽创建标注</p>
                  <p>• 点击标注可以选中，在右侧列表中编辑标签</p>
                  <p>• 使用缩放工具可以放大查看细节</p>
                  <p>• 支持撤销/重做操作，可以随时恢复之前的状态</p>
                </div>
              </div>

              {/* Statistics */}
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{annotations.length}</div>
                  <div className="text-sm text-gray-600">总标注数</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{annotations.filter(a => a.label).length}</div>
                  <div className="text-sm text-gray-600">已标记</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{Math.round(zoom * 100)}%</div>
                  <div className="text-sm text-gray-600">缩放比例</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{history.length}</div>
                  <div className="text-sm text-gray-600">历史记录</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
