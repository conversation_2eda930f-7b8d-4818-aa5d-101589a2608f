"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-selector.tsx":
/*!**************************************************!*\
  !*** ./components/annotation/image-selector.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageSelector: () => (/* binding */ ImageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Image,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ ImageSelector auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImageSelector(param) {\n    let { onImageSelect, selectedImageId, datasetId } = param;\n    _s();\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFormat, setSelectedFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageSelector.useEffect\": ()=>{\n            loadImages();\n        }\n    }[\"ImageSelector.useEffect\"], [\n        datasetId,\n        selectedFormat,\n        selectedTags\n    ]);\n    const loadImages = async ()=>{\n        setLoading(true);\n        try {\n            const params = {\n                datasetId,\n                format: selectedFormat === 'all' ? undefined : selectedFormat,\n                tags: selectedTags.length > 0 ? selectedTags : undefined\n            };\n            const imageList = await _services_imageService__WEBPACK_IMPORTED_MODULE_6__.imageService.getImages(params);\n            setImages(imageList);\n        } catch (error) {\n            console.error('Failed to load images:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Filter images by search term\n    const filteredImages = images.filter((image)=>{\n        var _image_tags;\n        return image.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_image_tags = image.tags) === null || _image_tags === void 0 ? void 0 : _image_tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    });\n    // Get unique formats and tags\n    const availableFormats = [\n        ...new Set(images.map((img)=>img.format))\n    ];\n    const availableTags = [\n        ...new Set(images.flatMap((img)=>img.tags || []))\n    ];\n    const handleTagToggle = (tag)=>{\n        setSelectedTags((prev)=>prev.includes(tag) ? prev.filter((t)=>t !== tag) : [\n                ...prev,\n                tag\n            ]);\n    };\n    const handleRandomSelect = async ()=>{\n        try {\n            const randomImage = await _services_imageService__WEBPACK_IMPORTED_MODULE_6__.imageService.getRandomImage(datasetId);\n            if (randomImage) {\n                onImageSelect(randomImage);\n            }\n        } catch (error) {\n            console.error('Failed to get random image:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"选择图像\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: \"正在加载图像列表...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-6 w-6 animate-spin text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            \"选择图像\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: [\n                            \"选择要进行标注的图像 (\",\n                            filteredImages.length,\n                            \" 张可用)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"搜索图像名称或标签...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: selectedFormat,\n                                        onValueChange: setSelectedFormat,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                    placeholder: \"格式\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"所有格式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    availableFormats.map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: format,\n                                                            children: format.toUpperCase()\n                                                        }, format, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleRandomSelect,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"随机选择\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            availableTags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"标签筛选:\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: availableTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-2 py-1 text-xs rounded-full border transition-colors cursor-pointer \".concat(selectedTags.includes(tag) ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'),\n                                                onClick: ()=>handleTagToggle(tag),\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3 max-h-96 overflow-y-auto\",\n                        children: filteredImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative cursor-pointer rounded-lg border-2 transition-all \".concat(selectedImageId === image.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'),\n                                onClick: ()=>onImageSelect(image),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: image.url,\n                                            alt: image.name,\n                                            className: \"w-full h-full object-cover\",\n                                            loading: \"lazy\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium truncate\",\n                                                children: image.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    image.width,\n                                                    \"\\xd7\",\n                                                    image.height,\n                                                    \" • \",\n                                                    (image.size / 1024).toFixed(1),\n                                                    \"KB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            image.tags && image.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                children: [\n                                                    image.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded\",\n                                                            children: tag\n                                                        }, tag, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 23\n                                                        }, this)),\n                                                    image.tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-1 py-0 bg-gray-100 text-gray-600 text-xs rounded\",\n                                                        children: [\n                                                            \"+\",\n                                                            image.tags.length - 2\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, image.id, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    filteredImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Image_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-2 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"没有找到匹配的图像\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"尝试调整搜索条件或筛选器\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-selector.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageSelector, \"cT+WXUdh5uud5stShw9g4Jm8hrg=\");\n_c = ImageSelector;\nvar _c;\n$RefreshReg$(_c, \"ImageSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-selector.tsx\n"));

/***/ })

});